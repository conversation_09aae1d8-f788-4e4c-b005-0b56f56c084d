{"name": "vue-element-admin", "version": "4.4.0", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "Pan <<EMAIL>>", "scripts": {"dev": "vue-cli-service serve", "lint": "eslint --ext .js,.vue src --ignore-pattern 'src/compat/**' --ignore-pattern 'src/views/design-system/**'", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "new": "plop", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "t:prettier": "gogocode -s ./src -t /Users/<USER>/project/github/gogocode/packages/gogocode-plugin-prettier/index.js -o ./src", "t:vue": "gogocode -s ./src -t /Users/<USER>/project/github/gogocode/packages/gogocode-plugin-vue/index.js -o ./src", "t:pkg": "gogocode -s package.json -t /Users/<USER>/project/github/gogocode/packages/gogocode-plugin-vue/index.js -o package.json", "t:element": "gogocode -s ./src -t /Users/<USER>/project/github/gogocode/packages/gogocode-plugin-element/index.js -o ./src"}, "dependencies": {"@element-plus/icons": "^0.0.11", "@element-plus/icons-vue": "^2.3.1", "@vue/preload-webpack-plugin": "^2.0.0", "ag-grid-community": "^33.3.2", "ag-grid-vue3": "^33.3.2", "axios": "0.18.1", "clipboard": "2.0.4", "codemirror": "5.45.0", "core-js": "^3.8.3", "dayjs": "^1.11.13", "driver.js": "0.9.5", "dropzone": "5.5.1", "echarts": "4.2.1", "element-plus": "^2.0.2", "file-saver": "2.0.1", "fuse.js": "3.4.4", "js-cookie": "2.2.0", "jsonlint": "1.6.3", "jszip": "3.2.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "screenfull": "4.2.0", "sortablejs": "1.8.4", "tiny-emitter": "^2.1.0", "tui-editor": "1.3.3", "vue": "^3.2.13", "vue-router": "^4.0.8", "vue3-count-to": "^1.1.2", "vuedraggable": "2.20.0", "vuex": "^4.0.2", "xlsx": "0.14.1"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@types/eslint": "^9.6.1", "@types/node": "^24.0.3", "@vitest/eslint-plugin": "^1.2.7", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/test-utils": "1.0.0-beta.29", "@vue/tsconfig": "^0.7.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "eslint": "^7.32.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "^8.0.3", "husky": "1.3.1", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "path-browserify": "^1.0.1", "plop": "2.3.0", "postcss": "^8.4.6", "runjs": "4.3.2", "sass": "^1.49.8", "sass-loader": "^12.6.0", "serve-static": "1.13.2", "stream-browserify": "^3.0.0", "svg-sprite-loader": "^6.0.11", "svgo": "1.2.0", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "webpack-env": "^0.8.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "globals": {"defineProps": "readonly", "defineEmits": "readonly"}, "rules": {"vue/multi-word-component-names": "off", "vue/no-mutating-props": "off", "no-unused-vars": "off", "no-useless-escape": "off", "no-prototype-builtins": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "bugs": {"url": "https://github.com/PanJiaChen/vue-element-admin/issues"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "license": "MIT", "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "husky": {"hooks": {}}, "repository": {"type": "git", "url": "git+https://github.com/PanJiaChen/vue-element-admin.git"}}