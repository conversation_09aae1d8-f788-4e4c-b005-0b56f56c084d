# DataGrid 组件使用文档

## 概述

DataGrid 是一个基于 Element Plus Table 的高级数据表格组件，提供配置化列生成、数据源抽象、操作列统一、自定义列渲染、高级筛选等功能。完全兼容 Element Plus Table 的 API，支持一键切换。

## 主要特性

- ✅ **配置化列生成** - 通过 JSON 配置快速生成表格列
- ✅ **数据源抽象** - 支持本地数据和远程数据，内置分页/排序/筛选
- ✅ **操作列统一** - 统一的操作列配置和按钮管理
- ✅ **自定义列渲染** - 支持自定义表头和单元格渲染
- ✅ **高级筛选集成** - 可配置的高级筛选表单
- ✅ **JSON Schema Form** - 基于 JSON Schema 的动态表单生成
- ✅ **表单联动逻辑** - 支持字段间的依赖关系和联动
- ✅ **自定义组件注册** - 可扩展的自定义组件系统
- ✅ **Element Plus 兼容** - 完全兼容 Element Plus Table API

## 快速开始

### 基础使用

```vue
<template>
  <DataGrid :config="gridConfig" />
</template>

<script setup>
import { ref } from 'vue'
import DataGrid from '@/components/common/DataGrid.vue'

const gridConfig = ref({
  columns: [
    { prop: 'id', label: 'ID', width: 80 },
    { prop: 'name', label: '姓名', minWidth: 120 },
    { prop: 'email', label: '邮箱', minWidth: 180 }
  ],
  dataSource: {
    data: [
      { id: 1, name: '张三', email: '<EMAIL>' },
      { id: 2, name: '李四', email: '<EMAIL>' }
    ]
  }
})
</script>
```

### 远程数据

```javascript
const gridConfig = {
  columns: [
    { prop: 'id', label: 'ID', width: 80 },
    { prop: 'title', label: '标题', minWidth: 200 }
  ],
  dataSource: {
    remote: {
      url: '/api/posts',
      method: 'GET',
      transformResponse: (response) => ({
        data: response.data,
        total: response.total,
        page: response.page,
        pageSize: response.pageSize
      })
    },
    pagination: {
      enabled: true,
      pageSize: 20
    }
  }
}
```

### 操作列

```javascript
const gridConfig = {
  // ... 其他配置
  actionColumn: {
    label: '操作',
    width: 200,
    actions: [
      {
        text: '编辑',
        type: 'primary',
        onClick: (row, index) => {
          console.log('编辑', row)
        }
      },
      {
        text: '删除',
        type: 'danger',
        disabled: (row) => row.status === 'locked',
        onClick: (row, index) => {
          console.log('删除', row)
        }
      }
    ]
  }
}
```

### 高级筛选

```javascript
const gridConfig = {
  // ... 其他配置
  advancedFilter: {
    enabled: true,
    collapsible: true,
    fields: [
      {
        prop: 'name',
        label: '姓名',
        type: 'input',
        placeholder: '请输入姓名'
      },
      {
        prop: 'status',
        label: '状态',
        type: 'select',
        options: [
          { label: '活跃', value: 'active' },
          { label: '非活跃', value: 'inactive' }
        ]
      }
    ]
  }
}
```

## 配置详解

### DataGridConfig

| 属性 | 类型 | 描述 |
|------|------|------|
| columns | `DataGridColumn[]` | 列配置数组 |
| dataSource | `DataSourceConfig` | 数据源配置 |
| actionColumn | `ActionColumnConfig` | 操作列配置 |
| advancedFilter | `AdvancedFilterConfig` | 高级筛选配置 |
| tableProps | `Partial<TableProps>` | Element Plus Table 原生属性 |
| events | `Object` | 事件配置 |

### DataGridColumn

| 属性 | 类型 | 描述 |
|------|------|------|
| prop | `string` | 字段名 |
| label | `string` | 列标题 |
| type | `'text' \| 'number' \| 'date' \| 'boolean' \| 'custom'` | 列类型 |
| width | `string \| number` | 列宽度 |
| sortable | `boolean \| 'custom'` | 是否可排序 |
| filterable | `boolean` | 是否可筛选 |
| renderHeader | `Function` | 自定义表头渲染 |
| renderCell | `Function` | 自定义单元格渲染 |

### DataSourceConfig

| 属性 | 类型 | 描述 |
|------|------|------|
| data | `any[]` | 本地数据 |
| remote | `Object` | 远程数据配置 |
| pagination | `Object` | 分页配置 |
| sorting | `Object` | 排序配置 |
| filtering | `Object` | 筛选配置 |

### ActionColumnConfig

| 属性 | 类型 | 描述 |
|------|------|------|
| label | `string` | 操作列标题 |
| width | `string \| number` | 操作列宽度 |
| actions | `ActionButtonConfig[]` | 操作按钮配置 |

## JSON Schema Form

### 基础用法

```vue
<template>
  <JsonSchemaForm v-model="formData" :config="formConfig" />
</template>

<script setup>
const formConfig = {
  schema: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        title: '姓名',
        minLength: 2
      },
      age: {
        type: 'number',
        title: '年龄',
        minimum: 18
      }
    },
    required: ['name']
  },
  uiSchema: {
    name: {
      'ui:placeholder': '请输入姓名'
    }
  }
}
</script>
```

### 高级特性

#### 字段联动

```javascript
const formConfig = {
  schema: {
    type: 'object',
    properties: {
      country: {
        type: 'string',
        title: '国家',
        enum: ['china', 'usa']
      },
      city: {
        type: 'string',
        title: '城市'
      }
    }
  },
  // 使用表单依赖系统
  fields: [
    {
      prop: 'country',
      label: '国家',
      type: 'select',
      options: [
        { label: '中国', value: 'china' },
        { label: '美国', value: 'usa' }
      ]
    },
    {
      prop: 'city',
      label: '城市',
      type: 'select',
      dependencies: ['country'],
      dependencyLogic: (formData, [country]) => {
        const cityOptions = {
          china: [
            { label: '北京', value: 'beijing' },
            { label: '上海', value: 'shanghai' }
          ],
          usa: [
            { label: '纽约', value: 'newyork' },
            { label: '洛杉矶', value: 'losangeles' }
          ]
        }
        return {
          options: cityOptions[country] || []
        }
      }
    }
  ]
}
```

## 自定义组件

### 注册自定义组件

```javascript
import { useCustomComponents } from '@/composables/useCustomComponents'

const { registerComponent } = useCustomComponents()

// 注册自定义组件
registerComponent({
  name: 'my-custom-input',
  component: MyCustomInput,
  props: ['modelValue', 'disabled', 'placeholder'],
  events: ['update:modelValue', 'change']
})
```

### 在表单中使用

```javascript
const formConfig = {
  schema: {
    type: 'object',
    properties: {
      customField: {
        type: 'string',
        title: '自定义字段'
      }
    }
  },
  uiSchema: {
    customField: {
      'ui:widget': 'custom',
      'ui:options': {
        component: 'my-custom-input',
        componentProps: {
          placeholder: '请输入自定义内容'
        }
      }
    }
  }
}
```

## API 参考

### DataGrid 实例方法

| 方法 | 参数 | 描述 |
|------|------|------|
| refresh | `()` | 刷新数据 |
| search | `(params?)` | 搜索数据 |
| resetSearch | `()` | 重置搜索 |
| clearSelection | `()` | 清空选择 |
| exportData | `(format, options?)` | 导出数据 |

### 事件

| 事件名 | 参数 | 描述 |
|--------|------|------|
| selection-change | `selection` | 选择变化 |
| sort-change | `{ column, prop, order }` | 排序变化 |
| filter-change | `filters` | 筛选变化 |
| row-click | `row, column, event` | 行点击 |

## Element Plus Table 兼容性

DataGrid 完全兼容 Element Plus Table 的 API，可以通过 `tableProps` 传递所有原生属性：

```javascript
const gridConfig = {
  // ... 其他配置
  tableProps: {
    stripe: true,
    border: true,
    size: 'large',
    highlightCurrentRow: true,
    rowKey: 'id',
    // 所有 Element Plus Table 的属性都支持
  }
}
```

## 最佳实践

### 1. 列配置优化

```javascript
// 推荐：使用类型化的列配置
const columns = [
  {
    prop: 'createTime',
    label: '创建时间',
    type: 'date',
    width: 180,
    sortable: true,
    showOverflowTooltip: true
  },
  {
    prop: 'status',
    label: '状态',
    type: 'boolean',
    width: 100,
    renderCell: ({ row }) => {
      return h(ElTag, {
        type: row.status ? 'success' : 'danger'
      }, row.status ? '启用' : '禁用')
    }
  }
]
```

### 2. 数据源配置

```javascript
// 推荐：使用转换函数标准化数据格式
const dataSource = {
  remote: {
    url: '/api/users',
    transformRequest: (params) => {
      // 转换请求参数格式
      return {
        page: params.page,
        size: params.pageSize,
        search: params.name
      }
    },
    transformResponse: (response) => {
      // 转换响应数据格式
      return {
        data: response.content,
        total: response.totalElements,
        page: response.number + 1,
        pageSize: response.size
      }
    }
  }
}
```

### 3. 性能优化

```javascript
// 推荐：大数据量时启用虚拟滚动
const gridConfig = {
  tableProps: {
    // 使用 Element Plus 的虚拟化表格
    virtualized: true,
    height: 400
  }
}
```

## 常见问题

### Q: 如何自定义列渲染？

A: 使用 `renderCell` 函数：

```javascript
{
  prop: 'avatar',
  label: '头像',
  renderCell: ({ row }) => {
    return h('img', {
      src: row.avatar,
      style: { width: '40px', height: '40px', borderRadius: '50%' }
    })
  }
}
```

### Q: 如何处理大数据量？

A: 建议使用远程分页和 Element Plus 的虚拟化表格：

```javascript
const gridConfig = {
  dataSource: {
    remote: { /* 远程配置 */ },
    pagination: { enabled: true, pageSize: 50 }
  },
  tableProps: {
    virtualized: true,
    height: 600
  }
}
```

### Q: 如何实现复杂的筛选逻辑？

A: 使用高级筛选配置和自定义筛选组件：

```javascript
const advancedFilter = {
  enabled: true,
  fields: [
    {
      prop: 'dateRange',
      label: '日期范围',
      type: 'custom',
      component: 'date-range-picker',
      dependencies: ['type'],
      dependencyLogic: (formData, [type]) => {
        return {
          visible: type === 'timeFilter'
        }
      }
    }
  ]
}
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持配置化列生成
- 支持数据源抽象
- 支持操作列统一
- 支持高级筛选
- 支持 JSON Schema Form
- 完全兼容 Element Plus Table API

## 贡献指南

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License
