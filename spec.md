## 1. 项目结构规范

```
vue-project/
├── public/                     # 静态资源
│   ├── index.html
│   └── favicon.ico
├── src/
│   ├── api/                    # API 接口管理
│   │   ├── modules/
│   │   │   ├── user.js
│   │   │   └── product.js
│   │   └── index.js
│   ├── assets/                 # 静态资源
│   │   ├── images/
│   │   ├── icons/
│   │   └── fonts/
│   ├── components/             # 通用组件
│   │   ├── common/             # 基础组件
│   │   │   ├── BaseButton/
│   │   │   │   ├── index.vue
│   │   │   │   └── index.scss
│   │   │   └── BaseInput/
│   │   └── business/           # 业务组件
│   │       ├── UserCard/
│   │       └── ProductList/
│   ├── composables/            # Vue 3 组合式函数
│   │   ├── useAuth.js
│   │   └── useRequest.js
│   ├── constants/              # 常量定义
│   │   ├── index.js
│   │   └── enum.js
│   ├── directives/             # 自定义指令
│   │   ├── index.js
│   │   └── modules/
│   │       └── clickOutside.js
│   ├── layouts/                # 布局组件
│   │   ├── DefaultLayout.vue
│   │   └── AuthLayout.vue
│   ├── views/                  # 页面组件
│   │   ├── home/
│   │   │   ├── index.vue
│   │   │   └── components/     # 页面级组件
│   │   │       └── Banner.vue
│   │   └── user/
│   │       ├── index.vue
│   │       └── components/
│   ├── router/                 # 路由配置
│   │   ├── index.js
│   │   └── modules/
│   │       ├── home.js
│   │       └── user.js
│   ├── store/                  # 状态管理
│   │   ├── index.js
│   │   └── modules/
│   │       ├── user.js
│   │       └── app.js
│   ├── styles/                 # 样式文件
│   │   ├── abstracts/          # SCSS 抽象层
│   │   │   ├── _variables.scss
│   │   │   ├── _colors.scss
│   │   │   ├── _mixins.scss
│   │   │   └── _functions.scss
│   │   ├── base/               # 基础样式
│   │   │   ├── _reset.scss
│   │   │   └── _typography.scss
│   │   ├── vendors/            # 新增：管理第三方库的样式，如 _element-plus
│   │   │   └── _element-plus.scss
│   │   └── main.scss           # 主样式入口
│   ├── types/                  # TypeScript 类型定义
│   │   ├── index.d.ts
│   │   └── api.d.ts
│   ├── utils/                  # 工具函数
│   │   ├── request.js
│   │   ├── storage.js
│   │   ├── formatter.js
│   │   └── validators.js
│   ├── hooks/                  # 公共钩子函数
│   │   └── index.js
│   ├── App.vue
│   └── main.js
├── tests/                      # 测试文件
│   ├── unit/
│   └── e2e/
├── .env                        # 环境变量
├── .env.development
├── .env.production
├── .eslintrc.js
├── .prettierrc.js
├── .stylelintrc.js
├── .gitignore
├── babel.config.js
├── package.json
├── README.md
└── vue.config.js / vite.config.js
```

## 2. JavaScript/TypeScript规范

### 2.1 基本规范

- 使用 ES6+ 语法，避免使用过时的 ES5 语法
- 优先使用 `const`，其次使用 `let`，避免使用 `var`
- 使用箭头函数表示匿名函数
- 优先使用模板字符串而非字符串拼接
- 使用解构赋值简化代码
- 使用 Promise 或 async/await 处理异步操作

### 2.2 命名规范

- 变量和函数名：使用 camelCase（小驼峰）
- 常量：使用 UPPER_SNAKE_CASE（大写蛇形）
- 类名和构造函数：使用 PascalCase（大驼峰）
- 私有属性和方法：使用 `_` 前缀（如 `_privateMethod`）
- 布尔类型变量使用 `is`/`has`/`should` 等前缀（如 `isLoading`）

### 2.3 TypeScript规范（推荐）

- 明确定义接口和类型，避免使用 `any`
- 使用 TypeScript 的高级类型（联合类型、交叉类型、泛型等）
- 为函数参数和返回值添加类型注解
- 在 `types/` 目录下集中管理全局类型定义
- 使用 `interface` 定义对象结构，使用 `type` 定义类型别名

```typescript
// 推荐的类型定义方式
interface User {
  id: number;
  name: string;
  email: string;
  isActive: boolean;
}

type UserResponse = {
  data: User;
  status: number;
}
```

## 3. CSS/SCSS规范

### 3.1 基本规范

- 使用 SCSS 作为预处理器
- 使用 ITCSS 架构组织 CSS 代码（Inverted Triangle CSS）
- 在组件内使用 scoped 样式或 CSS Modules 避免样式污染
- 避免使用!important，除非必要

### 3.2 命名规范

- 使用 BEM 命名方法论
    - Block（块）：独立实体，有意义的独立元素（如 `.card`）
    - Element（元素）：块的组成部分（如 `.card__title`）
    - Modifier（修饰符）：改变块或元素的状态或外观（如 `.card--featured`）

```scss
// BEM 命名示例
.product-card {
  &__image {
    // 产品卡片的图片
  }
  
  &__title {
    // 产品卡片的标题
  }
  
  &--featured {
    // 特色产品卡片的样式
  }
}
```

### 3.3 样式组织

- 在 `styles/abstracts/_variables.scss` 中定义全局样式变量
- 在 `styles/abstracts/_colors.scss` 中集中管理颜色变量
- 使用 `styles/element-plus-variable.scss` 自定义 Element Plus 主题
- 避免深层嵌套，最多不超过3层
- 使用 mixins 复用常见样式模式

```scss
// 颜色变量示例
$primary-color: #3366ff;
$secondary-color: #5c7cfa;
$success-color: #38d9a9;
$warning-color: #fcc419;
$error-color: #fa5252;
$text-color-primary: #212529;
$text-color-secondary: #495057;
$background-color: #ffffff;
```

## 4. Vue组件规范

### 4.1 组件命名

- 组件名使用 PascalCase（大驼峰）
- 基础组件以 `Base` 前缀命名（如 `BaseButton.vue`）
- 紧密耦合的组件使用父组件名作为前缀（如 `UserProfileCard.vue`）
- 组件文件名与组件名保持一致，如：xxxx

### 4.2 单文件组件结构

- 推荐的组件结构顺序（Vue 3）：
    1. `<script setup>` 或 `<script>`
    2. `<template>`
    3. `<style>`

- 在大型组件中，可以使用标记注释划分逻辑区块

```vue
<script setup>
// 导入依赖
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'

// 数据定义
const count = ref(0)
const store = useStore()

// 计算属性
const doubleCount = computed(() => count.value * 2)

// 方法定义
function increment() {
  count.value++
}

// 生命周期钩子
onMounted(() => {
  console.log('Component mounted')
})
</script>

<template>
  <div class="counter">
    <p>Count: {{ count }}</p>
    <p>Double: {{ doubleCount }}</p>
    <button @click="increment">Increment</button>
  </div>
</template>

<style lang="scss" scoped>
.counter {
  padding: 16px;
  
  button {
    margin-top: 8px;
  }
}
</style>
```

### 4.3 组件编码规范

- 优先使用组合式 API（Composition API）
- 推荐使用 `<script setup>` 语法
- 将可复用的逻辑提取到 composables 中
- 组件的 props 应该使用对象形式定义类型和默认值
- 避免在组件中直接修改 props
- 使用 emits 选项明确声明组件触发的事件

```vue
<script setup>
import { ref } from 'vue'

// 定义 props
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  items: {
    type: Array,
    default: () => []
  }
})

// 定义 emits
const emit = defineEmits(['update', 'delete'])

// 组件逻辑
const isExpanded = ref(false)

function toggleExpand() {
  isExpanded.value = !isExpanded.value
  emit('update', { expanded: isExpanded.value })
}
</script>
```

### 4.4 组件通信

- 父子组件通信：使用 props 和 emits
- 兄弟组件通信：通过父组件或使用 provide/inject
- 全局状态管理：使用 Vuex/Pinia
- 复杂嵌套组件：使用 provide/inject

### 4.5 组件文档

- 为公共组件编写使用文档
- 使用 JSDoc 格式注释组件、props 和方法

## 5. 路由规范

### 5.1 基本规则

- 所有页面组件定义在 `views/` 目录下
- 路由配置文件按模块拆分，放在 `router/modules/` 目录下
- 使用路由懒加载减少首屏加载时间

### 5.2 路由命名规范

- 路由 path 使用 kebab-case（如 `/user-profile`）
- 路由 name 使用 camelCase（如 `userProfile`）
- 嵌套路由使用嵌套结构表示父子关系

```javascript
// router/modules/user.js
export default [
  {
    path: '/user',
    component: () => import('@/layouts/DefaultLayout.vue'),
    children: [
      {
        path: '',
        name: 'userList',
        component: () => import('@/views/user/index.vue'),
        meta: {
          title: '用户列表',
          requiresAuth: true
        }
      },
      {
        path: ':id',
        name: 'userDetail',
        component: () => import('@/views/user/detail.vue'),
        meta: {
          title: '用户详情',
          requiresAuth: true
        }
      },
      {
        path: 'create',
        name: 'userCreate',
        component: () => import('@/views/user/create.vue'),
        meta: {
          title: '创建用户',
          requiresAuth: true,
          permissions: ['admin']
        }
      }
    ]
  }
]
```

### 5.3 路由守卫

- 使用全局前置守卫处理鉴权逻辑
- 使用全局后置钩子处理页面标题等元信息
- 在路由 meta 字段中定义路由元信息

```javascript
// router/index.js
router.beforeEach((to, from, next) => {
  // 权限验证
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next({ name: 'login', query: { redirect: to.fullPath } })
  } else if (to.meta.permissions && !hasPermission(to.meta.permissions)) {
    next({ name: 'unauthorized' })
  } else {
    next()
  }
})

router.afterEach((to) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 应用名称` : '应用名称'
})
```

## 6. 状态管理规范

### 6.1 Vuex/Pinia 目录结构

```
store/
├── index.js              # Store 入口文件
├── modules/              # 模块目录
│   ├── user.js           # 用户模块
│   ├── product.js        # 产品模块
│   └── app.js            # 应用公共状态
```

### 6.2 模块化管理

- 按功能将 store 分割成模块
- 模块名使用 camelCase（小驼峰）
- 在大型应用中启用命名空间 `namespaced: true`

### 6.3 状态定义规范

- state 中只存储必要的应用状态
- getters 用于派生状态的计算
- mutations 用于同步修改状态
- actions 用于处理异步操作和复杂逻辑

```javascript
// store/modules/user.js
export default {
  namespaced: true,
  
  state: () => ({
    currentUser: null,
    isLoading: false,
    error: null
  }),
  
  getters: {
    isAuthenticated: state => !!state.currentUser,
    username: state => state.currentUser?.name || 'Guest',
    hasAdminRole: state => state.currentUser?.roles.includes('admin') || false
  },
  
  mutations: {
    SET_CURRENT_USER(state, user) {
      state.currentUser = user
    },
    SET_LOADING(state, status) {
      state.isLoading = status
    },
    SET_ERROR(state, error) {
      state.error = error
    }
  },
  
  actions: {
    async login({ commit }, credentials) {
      commit('SET_LOADING', true)
      commit('SET_ERROR', null)
      
      try {
        const user = await api.auth.login(credentials)
        commit('SET_CURRENT_USER', user)
        return user
      } catch (error) {
        commit('SET_ERROR', error.message)
        throw error
      } finally {
        commit('SET_LOADING', false)
      }
    },
    
    logout({ commit }) {
      commit('SET_CURRENT_USER', null)
      // 其他登出逻辑
    }
  }
}
```

### 6.4 使用建议

- 避免滥用全局状态，局部状态使用组件的 data 或 ref
- 短期状态或组件内状态使用 `ref/reactive` 或组合式 API
- 多组件共享但不需要持久化的状态可以使用 provide/inject
- 考虑使用 Vuex 持久化插件实现状态持久化

## 7. API接口管理规范

### 7.1 接口组织

- 按模块组织 API 请求函数
- 统一管理 API 请求路径
- 使用 axios 实例统一处理请求配置和拦截

```javascript
// api/index.js
import axios from 'axios'
import userApi from './modules/user'
import productApi from './modules/product'

// 创建 axios 实例
const request = axios.create({
  baseURL: process.env.VUE_APP_API_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 添加 token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => Promise.reject(error)
)

// 响应拦截器
request.interceptors.response.use(
  response => response.data,
  error => {
    // 统一错误处理
    if (error.response) {
      // 401 未授权
      if (error.response.status === 401) {
        // 跳转到登录页
      }
    }
    return Promise.reject(error)
  }
)

export default {
  user: userApi(request),
  product: productApi(request)
}
```

### 7.2 模块定义

```javascript
// api/modules/user.js
export default (request) => ({
  /**
   * 获取用户列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 用户列表和分页信息
   */
  getUsers(params) {
    return request.get('/users', { params })
  },
  
  /**
   * 获取用户详情
   * @param {number|string} id - 用户ID
   * @returns {Promise<Object>} 用户详情
   */
  getUserById(id) {
    return request.get(`/users/${id}`)
  },
  
  /**
   * 创建用户
   * @param {Object} userData - 用户数据
   * @returns {Promise<Object>} 创建的用户
   */
  createUser(userData) {
    return request.post('/users', userData)
  }
})
```

## 8. 技术选型与构建优化

### 8.1 Vue 版本选择

- 新项目推荐使用 Vue 3 + Composition API
- 存量 Vue 2 项目可使用 Vue CLI 5 + ESBuild

### 8.2 构建工具

- Vue 3 项目推荐使用 Vite 作为构建工具
- Vue 2 项目推荐使用 Vue CLI 5
- 考虑未来迁移需要时，可选择兼容两者的配置

### 8.3 UI 组件库选择

- Vue 3 项目推荐使用 Element Plus
- 有存量 Element UI 项目可方便迁移到 Element Plus
- 可以考虑按需引入减小包体积

### 8.4 状态管理

- Vue 3 项目可以使用 Pinia（推荐）或 Vuex 4
- Vue 2 项目使用 Vuex 3
- 简单项目可以考虑使用 provide/inject 代替完整的状态管理

## 9. 代码质量与工具配置（待定，与模板工程要保持一致）

### 9.1 ESLint 配置

```javascript
// .eslintrc.js
module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es2021: true,
  },
  extends: [
    'plugin:vue/vue3-recommended', // 或 'plugin:vue/recommended' (Vue 2)
    'eslint:recommended',
    '@vue/prettier'
  ],
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: 'module'
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'vue/component-name-in-template-casing': ['error', 'PascalCase'],
    'vue/no-v-html': 'warn',
    'vue/require-default-prop': 'error',
    'vue/require-prop-types': 'error'
  }
}
```

### 9.2 Prettier 配置

```javascript
// .prettierrc.js
module.exports = {
  semi: false,
  tabWidth: 2,
  useTabs: false,
  printWidth: 100,
  singleQuote: true,
  trailingComma: 'none',
  bracketSpacing: true,
  arrowParens: 'avoid',
  vueIndentScriptAndStyle: false
}
```

### 9.3 StyleLint 配置

```javascript
// .stylelintrc.js
module.exports = {
  extends: [
    'stylelint-config-standard-scss',
    'stylelint-config-recommended-vue/scss'
  ],
  rules: {
    'selector-class-pattern': null,
    'scss/dollar-variable-pattern': null,
    'selector-pseudo-class-no-unknown': [
      true,
      {
        ignorePseudoClasses: ['deep', 'global']
      }
    ]
  }
}
```

### 9.4 Git Hooks

- 使用 husky 和 lint-staged 在提交前运行代码检查
- 在 package.json 中配置 pre-commit 钩子

```json
{
  "lint-staged": {
    "*.{js,jsx,vue,ts,tsx}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{css,scss,less,vue}": [
      "stylelint --fix",
      "prettier --write"
    ]
  },
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged"
    }
  }
}
```

## 10. 性能优化指南

### 10.1 包体积优化

- 使用 `webpack-bundle-analyzer` 或 `rollup-plugin-visualizer` 分析包体积
- 路由懒加载减小初始加载体积
- 第三方库按需导入
- 使用 CDN 加载大型库
- 考虑使用 Tree-shaking 优化

### 10.2 构建速度优化

- 使用 ESBuild 或 SWC 替代 Babel
- 开发环境使用 Vite 获得更快的开发体验
- 使用缓存加速构建过程
- 优化 webpack/vite 配置

### 10.3 运行时性能优化

- 使用 `v-memo` 缓存模板部分
- 合理使用 `v-once` 处理一次性渲染内容
- 避免不必要的组件渲染
- 使用计算属性代替复杂模板表达式
- 为列表渲染提供唯一 key
- 使用 `keep-alive` 缓存组件

### 10.4 微前端架构

对于大型项目，可以考虑采用微前端架构，将应用拆分为多个子应用：

- 使用 qiankun 或 single-spa 作为微前端框架
- 主应用负责整体布局和子应用管理
- 子应用独立开发、部署和维护
- 可以混合使用 Vue 2 和 Vue 3 项目
- 通过共享运行时减小整体体积
