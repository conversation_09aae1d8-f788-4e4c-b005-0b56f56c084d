{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "vue-shim.d.ts"], "exclude": ["src/**/__tests__/*", "node_modules"], "compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "allowJs": true, "checkJs": false, "jsx": "preserve", "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "sourceMap": true, "composite": true, "noEmit": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["webpack-env", "node"], "isolatedModules": true, "importsNotUsedAsValues": "preserve", "preserveValueImports": true, "moduleResolution": "node"}}