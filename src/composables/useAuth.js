import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

/**
 * 用户认证相关的组合式函数
 * 提供登录、登出、权限检查等功能
 */
export function useAuth() {
  const router = useRouter()
  const store = useStore()
  
  const isLoading = ref(false)
  const error = ref(null)
  
  // 计算属性
  const isAuthenticated = computed(() => store.getters['user/isAuthenticated'])
  const currentUser = computed(() => store.getters['user/currentUser'])
  const userPermissions = computed(() => store.getters['user/userPermissions'])
  
  // 登录
  const login = async (credentials) => {
    try {
      isLoading.value = true
      error.value = null
      
      await store.dispatch('user/login', credentials)
      
      // 获取重定向路径或默认跳转到首页
      const redirect = router.currentRoute.value.query.redirect || '/'
      router.push(redirect)
      
      return true
    } catch (err) {
      error.value = err.message || '登录失败'
      return false
    } finally {
      isLoading.value = false
    }
  }
  
  // 登出
  const logout = async () => {
    try {
      await store.dispatch('user/logout')
      router.push('/login')
    } catch (err) {
      console.error('登出失败:', err)
    }
  }
  
  // 检查用户权限
  const hasPermission = (permission) => {
    if (!isAuthenticated.value) return false
    if (!permission) return true
    
    const permissions = userPermissions.value || []
    return permissions.includes(permission) || permissions.includes('*')
  }
  
  // 检查用户角色
  const hasRole = (role) => {
    if (!isAuthenticated.value) return false
    if (!role) return true
    
    const userRoles = currentUser.value?.roles || []
    return userRoles.includes(role) || userRoles.includes('admin')
  }
  
  // 刷新用户信息
  const refreshUser = async () => {
    try {
      await store.dispatch('user/fetchCurrentUser')
    } catch (err) {
      console.error('刷新用户信息失败:', err)
    }
  }
  
  return {
    // 状态
    isLoading,
    error,
    isAuthenticated,
    currentUser,
    userPermissions,
    
    // 方法
    login,
    logout,
    hasPermission,
    hasRole,
    refreshUser
  }
}
