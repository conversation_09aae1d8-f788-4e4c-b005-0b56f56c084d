import { ref, watch } from 'vue'

/**
 * localStorage 相关的组合式函数
 * 提供响应式的本地存储功能
 */
export function useLocalStorage(key, defaultValue = null, options = {}) {
  const {
    serializer = {
      read: JSON.parse,
      write: JSON.stringify
    },
    syncAcrossTabs = true
  } = options
  
  // 读取初始值
  const read = () => {
    try {
      const item = localStorage.getItem(key)
      if (item === null) return defaultValue
      return serializer.read(item)
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error)
      return defaultValue
    }
  }
  
  // 写入值
  const write = (value) => {
    try {
      if (value === null || value === undefined) {
        localStorage.removeItem(key)
      } else {
        localStorage.setItem(key, serializer.write(value))
      }
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error)
    }
  }
  
  const storedValue = ref(read())
  
  // 监听值的变化并同步到 localStorage
  watch(
    storedValue,
    (newValue) => {
      write(newValue)
    },
    { deep: true }
  )
  
  // 跨标签页同步
  if (syncAcrossTabs) {
    window.addEventListener('storage', (e) => {
      if (e.key === key && e.newValue !== null) {
        try {
          storedValue.value = serializer.read(e.newValue)
        } catch (error) {
          console.error(`Error parsing localStorage value for key "${key}":`, error)
        }
      }
    })
  }
  
  return storedValue
}

/**
 * sessionStorage 相关的组合式函数
 */
export function useSessionStorage(key, defaultValue = null, options = {}) {
  const {
    serializer = {
      read: JSON.parse,
      write: JSON.stringify
    }
  } = options
  
  const read = () => {
    try {
      const item = sessionStorage.getItem(key)
      if (item === null) return defaultValue
      return serializer.read(item)
    } catch (error) {
      console.error(`Error reading sessionStorage key "${key}":`, error)
      return defaultValue
    }
  }
  
  const write = (value) => {
    try {
      if (value === null || value === undefined) {
        sessionStorage.removeItem(key)
      } else {
        sessionStorage.setItem(key, serializer.write(value))
      }
    } catch (error) {
      console.error(`Error setting sessionStorage key "${key}":`, error)
    }
  }
  
  const storedValue = ref(read())
  
  watch(
    storedValue,
    (newValue) => {
      write(newValue)
    },
    { deep: true }
  )
  
  return storedValue
}
