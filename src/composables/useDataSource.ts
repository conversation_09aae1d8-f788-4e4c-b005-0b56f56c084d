import { ref } from 'vue'
import type { DataSourceConfig, DataSourceResponse } from '@/types/data-grid'
import { request } from '@/utils/request'

export function useDataSource(config: DataSourceConfig) {
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 获取数据
  const fetchData = async (params: Record<string, any> = {}): Promise<DataSourceResponse> => {
    loading.value = true
    error.value = null

    try {
      if (config.remote) {
        // 远程数据
        const { url, method = 'GET', headers, transformRequest, transformResponse } = config.remote
        
        let requestParams = { ...config.remote.params, ...params }
        
        // 转换请求参数
        if (transformRequest) {
          requestParams = transformRequest(requestParams)
        }

        const response = await request({
          url,
          method,
          headers,
          [method === 'GET' ? 'params' : 'data']: requestParams
        })

        // 转换响应数据
        if (transformResponse) {
          return transformResponse(response)
        }

        // 默认响应格式
        return {
          data: response.data || response,
          total: response.total || response.data?.length || 0,
          page: response.page || params.page || 1,
          pageSize: response.pageSize || params.pageSize || 20
        }
      } else {
        // 本地数据
        return {
          data: config.data || [],
          total: config.data?.length || 0,
          page: params.page || 1,
          pageSize: params.pageSize || 20
        }
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '数据加载失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 搜索数据
  const searchData = async (searchParams: Record<string, any>) => {
    return fetchData(searchParams)
  }

  // 重置搜索
  const resetSearchData = async () => {
    return fetchData({})
  }

  return {
    loading,
    error,
    fetchData,
    searchData,
    resetSearchData
  }
}
