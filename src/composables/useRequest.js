import { ref, unref } from 'vue'

/**
 * HTTP请求相关的组合式函数
 * 提供统一的请求状态管理、错误处理、缓存等功能
 */
export function useRequest(requestFn, options = {}) {
  const {
    immediate = false,
    initialData = null,
    onSuccess,
    onError,
    throwOnError = false
  } = options
  
  const data = ref(initialData)
  const loading = ref(false)
  const error = ref(null)
  const finished = ref(false)
  
  // 执行请求
  const execute = async (...args) => {
    try {
      loading.value = true
      error.value = null
      finished.value = false
      
      const result = await requestFn(...args)
      data.value = result
      
      onSuccess?.(result)
      return result
    } catch (err) {
      error.value = err
      onError?.(err)
      
      if (throwOnError) {
        throw err
      }
    } finally {
      loading.value = false
      finished.value = true
    }
  }
  
  // 重置状态
  const reset = () => {
    data.value = initialData
    loading.value = false
    error.value = null
    finished.value = false
  }
  
  // 立即执行
  if (immediate) {
    execute()
  }
  
  return {
    data,
    loading,
    error,
    finished,
    execute,
    reset
  }
}

/**
 * 分页请求的组合式函数
 */
export function usePagination(requestFn, options = {}) {
  const {
    pageSize = 10,
    immediate = true,
    ...requestOptions
  } = options
  
  const currentPage = ref(1)
  const total = ref(0)
  const pageCount = ref(0)
  
  const {
    data: rawData,
    loading,
    error,
    execute: executeRequest,
    reset
  } = useRequest(requestFn, requestOptions)
  
  // 格式化数据
  const data = ref([])
  
  const execute = async (page = 1) => {
    currentPage.value = page
    
    const params = {
      page,
      pageSize: unref(pageSize),
      ...options.params
    }
    
    const result = await executeRequest(params)
    
    if (result) {
      data.value = result.data || result.list || []
      total.value = result.total || 0
      pageCount.value = Math.ceil(total.value / unref(pageSize))
    }
    
    return result
  }
  
  // 跳转到指定页
  const goToPage = (page) => {
    if (page >= 1 && page <= pageCount.value) {
      execute(page)
    }
  }
  
  // 上一页
  const prev = () => {
    if (currentPage.value > 1) {
      execute(currentPage.value - 1)
    }
  }
  
  // 下一页
  const next = () => {
    if (currentPage.value < pageCount.value) {
      execute(currentPage.value + 1)
    }
  }
  
  // 刷新当前页
  const refresh = () => {
    execute(currentPage.value)
  }
  
  // 重置到第一页
  const resetPagination = () => {
    reset()
    currentPage.value = 1
    total.value = 0
    pageCount.value = 0
    data.value = []
  }
  
  // 立即执行
  if (immediate) {
    execute()
  }
  
  return {
    // 数据
    data,
    loading,
    error,
    
    // 分页信息
    currentPage,
    total,
    pageCount,
    pageSize,
    
    // 方法
    execute,
    goToPage,
    prev,
    next,
    refresh,
    reset: resetPagination
  }
}
