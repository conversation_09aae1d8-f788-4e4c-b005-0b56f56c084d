import { ref, reactive, computed, nextTick } from 'vue'

/**
 * 表单处理相关的组合式函数
 * 提供表单验证、提交、重置等功能
 */
export function useForm(initialValues = {}, rules = {}) {
  const formData = reactive({ ...initialValues })
  const errors = ref({})
  const touched = ref({})
  const isSubmitting = ref(false)
  
  // 计算属性
  const isValid = computed(() => {
    return Object.keys(errors.value).length === 0
  })
  
  const isDirty = computed(() => {
    return Object.keys(touched.value).some(key => touched.value[key])
  })
  
  // 验证单个字段
  const validateField = (field, value) => {
    const fieldRules = rules[field]
    if (!fieldRules) return true
    
    for (const rule of fieldRules) {
      const result = rule.validator(value, formData)
      if (!result) {
        errors.value[field] = rule.message
        return false
      }
    }
    
    delete errors.value[field]
    return true
  }
  
  // 验证所有字段
  const validate = () => {
    let isFormValid = true
    
    Object.keys(rules).forEach(field => {
      const isFieldValid = validateField(field, formData[field])
      if (!isFieldValid) {
        isFormValid = false
      }
    })
    
    return isFormValid
  }
  
  // 设置字段值
  const setFieldValue = (field, value) => {
    formData[field] = value
    touched.value[field] = true
    
    // 如果字段之前有错误，重新验证
    if (errors.value[field]) {
      validateField(field, value)
    }
  }
  
  // 设置字段错误
  const setFieldError = (field, message) => {
    errors.value[field] = message
  }
  
  // 清除字段错误
  const clearFieldError = (field) => {
    delete errors.value[field]
  }
  
  // 重置表单
  const resetForm = () => {
    Object.keys(formData).forEach(key => {
      formData[key] = initialValues[key]
    })
    errors.value = {}
    touched.value = {}
    isSubmitting.value = false
  }
  
  // 提交表单
  const handleSubmit = async (onSubmit) => {
    // 标记所有字段为已触摸
    Object.keys(formData).forEach(field => {
      touched.value[field] = true
    })
    
    if (!validate()) {
      return false
    }
    
    try {
      isSubmitting.value = true
      await onSubmit(formData)
      return true
    } catch (error) {
      console.error('表单提交失败:', error)
      return false
    } finally {
      isSubmitting.value = false
    }
  }
  
  return {
    // 表单数据
    formData,
    errors,
    touched,
    isSubmitting,
    
    // 计算属性
    isValid,
    isDirty,
    
    // 方法
    setFieldValue,
    setFieldError,
    clearFieldError,
    validateField,
    validate,
    resetForm,
    handleSubmit
  }
}

/**
 * 表单字段的组合式函数
 */
export function useField(name, initialValue = '', rules = []) {
  const value = ref(initialValue)
  const error = ref('')
  const touched = ref(false)
  
  const validate = () => {
    for (const rule of rules) {
      const result = rule.validator(value.value)
      if (!result) {
        error.value = rule.message
        return false
      }
    }
    
    error.value = ''
    return true
  }
  
  const setValue = (newValue) => {
    value.value = newValue
    touched.value = true
    
    // 如果字段之前有错误，重新验证
    if (error.value) {
      validate()
    }
  }
  
  const reset = () => {
    value.value = initialValue
    error.value = ''
    touched.value = false
  }
  
  return {
    name,
    value,
    error,
    touched,
    setValue,
    validate,
    reset
  }
}
