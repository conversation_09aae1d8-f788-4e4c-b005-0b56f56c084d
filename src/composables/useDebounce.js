import { ref, watch } from 'vue'

/**
 * 防抖相关的组合式函数
 */
export function useDebounce(value, delay = 300) {
  const debouncedValue = ref(value.value || value)
  let timeoutId = null
  
  const updateDebouncedValue = (newValue) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = setTimeout(() => {
      debouncedValue.value = newValue
    }, delay)
  }
  
  // 如果传入的是 ref，监听其变化
  if (value && typeof value === 'object' && 'value' in value) {
    watch(value, updateDebouncedValue, { immediate: true })
  } else {
    debouncedValue.value = value
  }
  
  return debouncedValue
}

/**
 * 防抖函数的组合式函数
 */
export function useDebouncedFn(fn, delay = 300) {
  let timeoutId = null
  
  const debouncedFn = (...args) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = setTimeout(() => {
      fn(...args)
    }, delay)
  }
  
  const cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
  }
  
  const flush = (...args) => {
    cancel()
    fn(...args)
  }
  
  return {
    debouncedFn,
    cancel,
    flush
  }
}

/**
 * 节流相关的组合式函数
 */
export function useThrottle(value, delay = 300) {
  const throttledValue = ref(value.value || value)
  let lastUpdateTime = 0
  let timeoutId = null
  
  const updateThrottledValue = (newValue) => {
    const now = Date.now()
    
    if (now - lastUpdateTime >= delay) {
      throttledValue.value = newValue
      lastUpdateTime = now
    } else {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      
      timeoutId = setTimeout(() => {
        throttledValue.value = newValue
        lastUpdateTime = Date.now()
      }, delay - (now - lastUpdateTime))
    }
  }
  
  // 如果传入的是 ref，监听其变化
  if (value && typeof value === 'object' && 'value' in value) {
    watch(value, updateThrottledValue, { immediate: true })
  } else {
    throttledValue.value = value
  }
  
  return throttledValue
}

/**
 * 节流函数的组合式函数
 */
export function useThrottledFn(fn, delay = 300) {
  let lastCallTime = 0
  let timeoutId = null
  
  const throttledFn = (...args) => {
    const now = Date.now()
    
    if (now - lastCallTime >= delay) {
      fn(...args)
      lastCallTime = now
    } else {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      
      timeoutId = setTimeout(() => {
        fn(...args)
        lastCallTime = Date.now()
      }, delay - (now - lastCallTime))
    }
  }
  
  const cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
  }
  
  return {
    throttledFn,
    cancel
  }
}
