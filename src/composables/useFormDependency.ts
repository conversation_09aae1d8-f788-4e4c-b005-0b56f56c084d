import { ref, computed, watch, reactive } from 'vue'
import type { FormFieldConfig } from '@/types/data-grid'

// 表单字段依赖关系管理
export function useFormDependency(fields: FormFieldConfig[], formData: any) {
  // 依赖关系映射
  const dependencyMap = ref(new Map<string, string[]>())
  
  // 字段配置映射
  const fieldConfigMap = ref(new Map<string, FormFieldConfig>())
  
  // 动态字段配置
  const dynamicConfigs = reactive<Record<string, Partial<FormFieldConfig>>>({})
  
  // 初始化依赖关系
  const initDependencies = () => {
    dependencyMap.value.clear()
    fieldConfigMap.value.clear()
    
    fields.forEach(field => {
      fieldConfigMap.value.set(field.prop, field)
      
      if (field.dependencies && field.dependencies.length > 0) {
        field.dependencies.forEach(dep => {
          if (!dependencyMap.value.has(dep)) {
            dependencyMap.value.set(dep, [])
          }
          dependencyMap.value.get(dep)!.push(field.prop)
        })
      }
    })
  }
  
  // 获取字段的动态配置
  const getFieldConfig = (prop: string): FormFieldConfig => {
    const baseConfig = fieldConfigMap.value.get(prop)
    const dynamicConfig = dynamicConfigs[prop]
    
    if (!baseConfig) {
      throw new Error(`Field config not found for prop: ${prop}`)
    }
    
    return { ...baseConfig, ...dynamicConfig }
  }
  
  // 更新字段依赖
  const updateFieldDependencies = (changedProp: string) => {
    const dependentFields = dependencyMap.value.get(changedProp) || []
    
    dependentFields.forEach(fieldProp => {
      const field = fieldConfigMap.value.get(fieldProp)
      if (field && field.dependencyLogic) {
        const dependencies = field.dependencies?.map(dep => formData[dep]) || []
        const changes = field.dependencyLogic(formData, dependencies)
        
        if (changes) {
          dynamicConfigs[fieldProp] = { ...dynamicConfigs[fieldProp], ...changes }
        }
      }
    })
  }
  
  // 批量更新所有依赖
  const updateAllDependencies = () => {
    fields.forEach(field => {
      if (field.dependencies && field.dependencyLogic) {
        const dependencies = field.dependencies.map(dep => formData[dep])
        const changes = field.dependencyLogic(formData, dependencies)
        
        if (changes) {
          dynamicConfigs[field.prop] = { ...dynamicConfigs[field.prop], ...changes }
        }
      }
    })
  }
  
  // 检查字段是否可见
  const isFieldVisible = (prop: string): boolean => {
    const config = getFieldConfig(prop)
    if (typeof config.visible === 'function') {
      return config.visible(formData)
    }
    return config.visible !== false
  }
  
  // 检查字段是否禁用
  const isFieldDisabled = (prop: string): boolean => {
    const config = getFieldConfig(prop)
    if (typeof config.disabled === 'function') {
      return config.disabled(formData)
    }
    return config.disabled || false
  }
  
  // 获取字段选项
  const getFieldOptions = (prop: string): Array<{ label: string; value: any }> => {
    const config = getFieldConfig(prop)
    return config.options || []
  }
  
  // 验证字段依赖
  const validateDependency = (prop: string): string[] => {
    const errors: string[] = []
    const field = fieldConfigMap.value.get(prop)
    
    if (!field || !field.dependencies) {
      return errors
    }
    
    // 检查依赖字段是否存在
    field.dependencies.forEach(dep => {
      if (!fieldConfigMap.value.has(dep)) {
        errors.push(`Dependency field '${dep}' not found for field '${prop}'`)
      }
    })
    
    // 检查循环依赖
    const visited = new Set<string>()
    const recursionStack = new Set<string>()
    
    const hasCyclicDependency = (currentProp: string): boolean => {
      if (recursionStack.has(currentProp)) {
        return true
      }
      if (visited.has(currentProp)) {
        return false
      }
      
      visited.add(currentProp)
      recursionStack.add(currentProp)
      
      const currentField = fieldConfigMap.value.get(currentProp)
      if (currentField?.dependencies) {
        for (const dep of currentField.dependencies) {
          if (hasCyclicDependency(dep)) {
            return true
          }
        }
      }
      
      recursionStack.delete(currentProp)
      return false
    }
    
    if (hasCyclicDependency(prop)) {
      errors.push(`Cyclic dependency detected for field '${prop}'`)
    }
    
    return errors
  }
  
  // 获取所有依赖验证错误
  const getAllDependencyErrors = (): Record<string, string[]> => {
    const errors: Record<string, string[]> = {}
    
    fields.forEach(field => {
      const fieldErrors = validateDependency(field.prop)
      if (fieldErrors.length > 0) {
        errors[field.prop] = fieldErrors
      }
    })
    
    return errors
  }
  
  // 获取字段依赖链
  const getDependencyChain = (prop: string): string[] => {
    const chain: string[] = []
    const visited = new Set<string>()
    
    const buildChain = (currentProp: string) => {
      if (visited.has(currentProp)) {
        return
      }
      
      visited.add(currentProp)
      chain.push(currentProp)
      
      const field = fieldConfigMap.value.get(currentProp)
      if (field?.dependencies) {
        field.dependencies.forEach(dep => {
          buildChain(dep)
        })
      }
    }
    
    buildChain(prop)
    return chain.reverse() // 依赖顺序（依赖的字段在前）
  }
  
  // 按依赖顺序排序字段
  const sortFieldsByDependency = (): FormFieldConfig[] => {
    const sorted: FormFieldConfig[] = []
    const visited = new Set<string>()
    const recursionStack = new Set<string>()
    
    const visit = (prop: string) => {
      if (recursionStack.has(prop)) {
        // 循环依赖，跳过
        return
      }
      if (visited.has(prop)) {
        return
      }
      
      visited.add(prop)
      recursionStack.add(prop)
      
      const field = fieldConfigMap.value.get(prop)
      if (field?.dependencies) {
        field.dependencies.forEach(dep => {
          visit(dep)
        })
      }
      
      recursionStack.delete(prop)
      
      if (field) {
        sorted.push(field)
      }
    }
    
    fields.forEach(field => {
      visit(field.prop)
    })
    
    return sorted
  }
  
  // 计算属性：可见字段
  const visibleFields = computed(() => {
    return fields.filter(field => isFieldVisible(field.prop))
  })
  
  // 计算属性：禁用字段
  const disabledFields = computed(() => {
    return fields.filter(field => isFieldDisabled(field.prop))
  })
  
  // 监听表单数据变化
  watch(
    () => formData,
    (newData, oldData) => {
      // 找出变化的字段
      const changedProps = Object.keys(newData).filter(
        key => newData[key] !== oldData?.[key]
      )
      
      // 更新相关依赖
      changedProps.forEach(prop => {
        updateFieldDependencies(prop)
      })
    },
    { deep: true }
  )
  
  // 监听字段配置变化
  watch(
    () => fields,
    () => {
      initDependencies()
      updateAllDependencies()
    },
    { deep: true, immediate: true }
  )
  
  return {
    dependencyMap,
    dynamicConfigs,
    getFieldConfig,
    updateFieldDependencies,
    updateAllDependencies,
    isFieldVisible,
    isFieldDisabled,
    getFieldOptions,
    validateDependency,
    getAllDependencyErrors,
    getDependencyChain,
    sortFieldsByDependency,
    visibleFields,
    disabledFields
  }
}

// 预定义的常用依赖逻辑
export const commonDependencyLogics = {
  // 显示/隐藏依赖
  showWhen: (dependencyProp: string, expectedValue: any) => {
    return (formData: any, dependencies: any[]) => {
      const actualValue = formData[dependencyProp]
      return {
        visible: actualValue === expectedValue
      }
    }
  },
  
  // 隐藏当依赖条件满足时
  hideWhen: (dependencyProp: string, expectedValue: any) => {
    return (formData: any, dependencies: any[]) => {
      const actualValue = formData[dependencyProp]
      return {
        visible: actualValue !== expectedValue
      }
    }
  },
  
  // 启用/禁用依赖
  enableWhen: (dependencyProp: string, expectedValue: any) => {
    return (formData: any, dependencies: any[]) => {
      const actualValue = formData[dependencyProp]
      return {
        disabled: actualValue !== expectedValue
      }
    }
  },
  
  // 禁用当依赖条件满足时
  disableWhen: (dependencyProp: string, expectedValue: any) => {
    return (formData: any, dependencies: any[]) => {
      const actualValue = formData[dependencyProp]
      return {
        disabled: actualValue === expectedValue
      }
    }
  },
  
  // 必填依赖
  requiredWhen: (dependencyProp: string, expectedValue: any) => {
    return (formData: any, dependencies: any[]) => {
      const actualValue = formData[dependencyProp]
      return {
        required: actualValue === expectedValue
      }
    }
  },
  
  // 动态选项依赖
  optionsFrom: (dependencyProp: string, optionsMap: Record<string, Array<{ label: string; value: any }>>) => {
    return (formData: any, dependencies: any[]) => {
      const actualValue = formData[dependencyProp]
      return {
        options: optionsMap[actualValue] || []
      }
    }
  },
  
  // 复合条件依赖
  when: (condition: (formData: any) => boolean, changes: Partial<FormFieldConfig>) => {
    return (formData: any, dependencies: any[]) => {
      return condition(formData) ? changes : {}
    }
  }
}
