import { ref, provide, inject, App } from 'vue'
import type { CustomComponent } from '@/types/data-grid'

// 全局自定义组件注册表
const globalComponents = new Map<string, CustomComponent>()

// 组合式函数：管理自定义组件
export function useCustomComponents() {
  // 本地组件注册表
  const localComponents = ref(new Map<string, CustomComponent>())
  
  // 注册组件
  const registerComponent = (component: CustomComponent) => {
    localComponents.value.set(component.name, component)
  }
  
  // 批量注册组件
  const registerComponents = (components: CustomComponent[]) => {
    components.forEach(component => {
      registerComponent(component)
    })
  }
  
  // 获取组件
  const getComponent = (name: string): CustomComponent | undefined => {
    return localComponents.value.get(name) || globalComponents.get(name)
  }
  
  // 获取所有组件
  const getAllComponents = (): Map<string, CustomComponent> => {
    const allComponents = new Map(globalComponents)
    localComponents.value.forEach((component, name) => {
      allComponents.set(name, component)
    })
    return allComponents
  }
  
  // 注销组件
  const unregisterComponent = (name: string) => {
    localComponents.value.delete(name)
  }
  
  // 清空本地组件
  const clearComponents = () => {
    localComponents.value.clear()
  }
  
  // 检查组件是否存在
  const hasComponent = (name: string): boolean => {
    return localComponents.value.has(name) || globalComponents.has(name)
  }
  
  // 提供给子组件
  provide('customComponents', getAllComponents())
  
  return {
    localComponents,
    registerComponent,
    registerComponents,
    getComponent,
    getAllComponents,
    unregisterComponent,
    clearComponents,
    hasComponent
  }
}

// 全局注册组件（用于插件安装）
export function installCustomComponent(component: CustomComponent) {
  globalComponents.set(component.name, component)
}

// 全局批量注册组件
export function installCustomComponents(components: CustomComponent[]) {
  components.forEach(component => {
    installCustomComponent(component)
  })
}

// 获取全局组件
export function getGlobalComponent(name: string): CustomComponent | undefined {
  return globalComponents.get(name)
}

// 获取所有全局组件
export function getAllGlobalComponents(): Map<string, CustomComponent> {
  return new Map(globalComponents)
}

// 注入自定义组件
export function useInjectCustomComponents(): Map<string, CustomComponent> {
  return inject('customComponents', new Map())
}

// Vue 插件：DataGrid 自定义组件系统
export const DataGridCustomComponentsPlugin = {
  install(app: App, options: { components?: CustomComponent[] } = {}) {
    // 注册全局组件
    if (options.components) {
      installCustomComponents(options.components)
    }
    
    // 提供全局注册方法
    app.config.globalProperties.$registerDataGridComponent = installCustomComponent
    app.config.globalProperties.$getDataGridComponent = getGlobalComponent
    app.config.globalProperties.$getAllDataGridComponents = getAllGlobalComponents
    
    // 提供全局组件配置
    app.provide('dataGridGlobalComponents', globalComponents)
  }
}

// 预定义的常用自定义组件示例
export const presetCustomComponents: CustomComponent[] = [
  {
    name: 'image-upload',
    component: {
      name: 'ImageUpload',
      props: ['modelValue', 'disabled', 'readonly', 'multiple', 'accept', 'maxSize', 'maxCount'],
      emits: ['update:modelValue', 'change', 'success', 'error'],
      template: `
        <div class="image-upload">
          <el-upload
            v-model:file-list="fileList"
            :action="action"
            :disabled="disabled"
            :multiple="multiple"
            :accept="accept || 'image/*'"
            :limit="maxCount"
            :auto-upload="false"
            list-type="picture-card"
            @change="handleChange"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </div>
      `
    },
    props: ['modelValue', 'disabled', 'readonly', 'multiple', 'accept', 'maxSize', 'maxCount'],
    events: ['update:modelValue', 'change', 'success', 'error']
  },
  {
    name: 'rich-text-editor',
    component: {
      name: 'RichTextEditor',
      props: ['modelValue', 'disabled', 'readonly', 'placeholder', 'height'],
      emits: ['update:modelValue', 'change', 'focus', 'blur'],
      template: `
        <div class="rich-text-editor">
          <div
            ref="editorRef"
            :style="{ height: height || '200px' }"
            contenteditable
            :placeholder="placeholder"
            @input="handleInput"
            @focus="$emit('focus', $event)"
            @blur="$emit('blur', $event)"
          ></div>
        </div>
      `
    },
    props: ['modelValue', 'disabled', 'readonly', 'placeholder', 'height'],
    events: ['update:modelValue', 'change', 'focus', 'blur']
  },
  {
    name: 'json-editor',
    component: {
      name: 'JsonEditor',
      props: ['modelValue', 'disabled', 'readonly', 'height'],
      emits: ['update:modelValue', 'change', 'error'],
      template: `
        <div class="json-editor">
          <el-input
            :model-value="jsonText"
            type="textarea"
            :rows="Math.ceil((height || 200) / 24)"
            :disabled="disabled"
            :readonly="readonly"
            placeholder="请输入 JSON 格式数据"
            @input="handleInput"
          />
        </div>
      `
    },
    props: ['modelValue', 'disabled', 'readonly', 'height'],
    events: ['update:modelValue', 'change', 'error']
  },
  {
    name: 'code-editor',
    component: {
      name: 'CodeEditor',
      props: ['modelValue', 'disabled', 'readonly', 'language', 'theme', 'height'],
      emits: ['update:modelValue', 'change'],
      template: `
        <div class="code-editor">
          <el-input
            :model-value="modelValue"
            type="textarea"
            :rows="Math.ceil((height || 200) / 24)"
            :disabled="disabled"
            :readonly="readonly"
            :placeholder="'请输入' + (language || 'code') + '代码'"
            @input="$emit('update:modelValue', $event)"
            @change="$emit('change', $event)"
          />
        </div>
      `
    },
    props: ['modelValue', 'disabled', 'readonly', 'language', 'theme', 'height'],
    events: ['update:modelValue', 'change']
  },
  {
    name: 'region-selector',
    component: {
      name: 'RegionSelector',
      props: ['modelValue', 'disabled', 'readonly', 'level'],
      emits: ['update:modelValue', 'change'],
      template: `
        <div class="region-selector">
          <el-cascader
            :model-value="modelValue"
            :disabled="disabled"
            placeholder="请选择地区"
            :options="regionOptions"
            :props="{ value: 'code', label: 'name', children: 'children' }"
            @update:model-value="$emit('update:modelValue', $event)"
            @change="$emit('change', $event)"
          />
        </div>
      `
    },
    props: ['modelValue', 'disabled', 'readonly', 'level'],
    events: ['update:modelValue', 'change']
  }
]
