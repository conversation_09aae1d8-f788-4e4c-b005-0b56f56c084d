// 注册自定义指令
import clickOutside from './modules/clickOutside'
import lazyLoad from './modules/lazyLoad'
import loading from './modules/loading'
import copy from './modules/copy'
import permission from './modules/permission'
import infiniteScroll from './modules/infiniteScroll'

// 所有自定义指令
const directives = {
  clickOutside,
  lazyLoad,
  loading,
  copy,
  permission,
  infiniteScroll
}

export default {
  install(app) {
    // 注册所有自定义指令
    Object.keys(directives).forEach(key => {
      // 将驼峰命名转换为短横线命名
      const directiveName = key.replace(/([A-Z])/g, '-$1').toLowerCase()
      app.directive(directiveName, directives[key])
    })
  }
}

// 导出单个指令供按需使用
export {
  clickOutside,
  lazyLoad,
  loading,
  copy,
  permission,
  infiniteScroll
}
