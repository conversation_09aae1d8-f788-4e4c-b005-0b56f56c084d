/**
 * 加载状态自定义指令
 * 为元素添加加载遮罩层
 */
export const loading = {
  beforeMount(el, binding) {
    // 创建加载遮罩元素
    const mask = document.createElement('div')
    mask.className = 'loading-mask'
    
    // 创建加载图标
    const spinner = document.createElement('div')
    spinner.className = 'loading-spinner'
    
    // 创建加载文本
    const text = document.createElement('div')
    text.className = 'loading-text'
    text.textContent = binding.arg || '加载中...'
    
    mask.appendChild(spinner)
    mask.appendChild(text)
    
    // 设置遮罩样式
    mask.style.cssText = `
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 999;
      backdrop-filter: blur(2px);
    `
    
    // 设置加载图标样式
    spinner.style.cssText = `
      width: 32px;
      height: 32px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #409eff;
      border-radius: 50%;
      animation: loading-spin 1s linear infinite;
    `
    
    // 设置加载文本样式
    text.style.cssText = `
      margin-top: 12px;
      color: #666;
      font-size: 14px;
    `
    
    // 添加动画样式
    if (!document.getElementById('loading-keyframes')) {
      const style = document.createElement('style')
      style.id = 'loading-keyframes'
      style.textContent = `
        @keyframes loading-spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `
      document.head.appendChild(style)
    }
    
    el._loadingMask = mask
    
    // 确保父元素有相对定位
    const position = window.getComputedStyle(el).position
    if (position === 'static') {
      el.style.position = 'relative'
    }
    
    // 根据绑定值决定是否显示
    if (binding.value) {
      el.appendChild(mask)
    }
  },
  
  updated(el, binding) {
    const mask = el._loadingMask
    if (!mask) return
    
    // 更新加载文本
    const textElement = mask.querySelector('.loading-text')
    if (textElement && binding.arg) {
      textElement.textContent = binding.arg
    }
    
    // 根据绑定值控制显示/隐藏
    if (binding.value) {
      if (!el.contains(mask)) {
        el.appendChild(mask)
      }
    } else {
      if (el.contains(mask)) {
        el.removeChild(mask)
      }
    }
  },
  
  unmounted(el) {
    // 清理遮罩元素
    if (el._loadingMask) {
      if (el.contains(el._loadingMask)) {
        el.removeChild(el._loadingMask)
      }
      delete el._loadingMask
    }
  }
}

export default loading
