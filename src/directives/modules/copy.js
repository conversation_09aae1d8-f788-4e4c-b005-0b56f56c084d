/**
 * 复制到剪贴板自定义指令
 * 点击元素时复制指定文本到剪贴板
 */
export const copy = {
  beforeMount(el, binding) {
    el._copyHandler = async () => {
      try {
        // 获取要复制的文本
        const text = binding.value || el.textContent || el.innerText
        
        // 使用现代 Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(text)
        } else {
          // 降级方案：使用 document.execCommand
          const textArea = document.createElement('textarea')
          textArea.value = text
          textArea.style.position = 'fixed'
          textArea.style.left = '-999999px'
          textArea.style.top = '-999999px'
          document.body.appendChild(textArea)
          textArea.focus()
          textArea.select()
          document.execCommand('copy')
          textArea.remove()
        }
        
        // 触发成功事件
        el.dispatchEvent(new CustomEvent('copy-success', {
          detail: { text }
        }))
        
        // 添加成功反馈样式
        el.classList.add('copy-success')
        setTimeout(() => {
          el.classList.remove('copy-success')
        }, 1000)
        
        // 如果有回调函数，调用它
        if (typeof binding.arg === 'function') {
          binding.arg(text)
        }
        
      } catch (error) {
        console.error('复制失败:', error)
        
        // 触发失败事件
        el.dispatchEvent(new CustomEvent('copy-error', {
          detail: { error }
        }))
        
        // 添加失败反馈样式
        el.classList.add('copy-error')
        setTimeout(() => {
          el.classList.remove('copy-error')
        }, 1000)
      }
    }
    
    // 添加点击事件监听器
    el.addEventListener('click', el._copyHandler)
    
    // 添加样式提示
    el.style.cursor = 'pointer'
    el.title = '点击复制'
  },
  
  updated(el, binding) {
    // 更新提示文本
    if (binding.arg && typeof binding.arg === 'string') {
      el.title = binding.arg
    }
  },
  
  unmounted(el) {
    // 清理事件监听器
    if (el._copyHandler) {
      el.removeEventListener('click', el._copyHandler)
      delete el._copyHandler
    }
  }
}

export default copy
