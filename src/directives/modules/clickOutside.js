/**
 * 点击外部区域触发的自定义指令
 * 常用于下拉菜单、弹窗等组件的关闭逻辑
 */
export const clickOutside = {
  beforeMount(el, binding) {
    el._clickOutsideHandler = (event) => {
      // 检查点击的目标是否在绑定元素内部
      if (!(el === event.target || el.contains(event.target))) {
        // 如果绑定的值是函数，则调用它
        if (typeof binding.value === 'function') {
          binding.value(event)
        }
      }
    }
    
    // 添加事件监听器
    document.addEventListener('click', el._clickOutsideHandler)
    document.addEventListener('touchstart', el._clickOutsideHandler)
  },
  
  unmounted(el) {
    // 清理事件监听器
    if (el._clickOutsideHandler) {
      document.removeEventListener('click', el._clickOutsideHandler)
      document.removeEventListener('touchstart', el._clickOutsideHandler)
      delete el._clickOutsideHandler
    }
  }
}

export default clickOutside
