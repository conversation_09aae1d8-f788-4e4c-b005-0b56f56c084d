/**
 * 权限控制自定义指令
 * 根据用户权限显示或隐藏元素
 */
export const permission = {
  beforeMount(el, binding) {
    checkPermission(el, binding)
  },
  
  updated(el, binding) {
    checkPermission(el, binding)
  }
}

function checkPermission(el, binding) {
  const { value, arg, modifiers } = binding
  
  // 获取当前用户权限（这里需要根据实际项目调整）
  const getUserPermissions = () => {
    // 从 store 或其他地方获取用户权限
    const store = window.$store || {}
    return store.getters?.['user/userPermissions'] || []
  }
  
  const getUserRoles = () => {
    const store = window.$store || {}
    return store.getters?.['user/userRoles'] || []
  }
  
  let hasPermission = false
  
  if (arg === 'role') {
    // 检查角色权限
    const userRoles = getUserRoles()
    if (Array.isArray(value)) {
      hasPermission = value.some(role => userRoles.includes(role))
    } else {
      hasPermission = userRoles.includes(value)
    }
  } else {
    // 检查操作权限
    const userPermissions = getUserPermissions()
    if (Array.isArray(value)) {
      if (modifiers.all) {
        // 需要拥有所有权限
        hasPermission = value.every(permission => userPermissions.includes(permission))
      } else {
        // 只需要拥有其中一个权限
        hasPermission = value.some(permission => userPermissions.includes(permission))
      }
    } else {
      hasPermission = userPermissions.includes(value)
    }
  }
  
  // 根据权限结果处理元素
  if (hasPermission) {
    // 有权限，显示元素
    el.style.display = ''
    el.style.visibility = ''
    el.removeAttribute('disabled')
  } else {
    if (modifiers.disabled) {
      // 无权限时禁用元素
      el.setAttribute('disabled', 'disabled')
      el.style.opacity = '0.5'
      el.style.cursor = 'not-allowed'
    } else if (modifiers.hidden) {
      // 无权限时隐藏元素（保留空间）
      el.style.visibility = 'hidden'
    } else {
      // 无权限时移除元素（不保留空间）
      el.style.display = 'none'
    }
  }
}

export default permission
