/**
 * 无限滚动自定义指令
 * 当滚动到底部时触发加载更多数据
 */
export const infiniteScroll = {
  beforeMount(el, binding) {
    const options = {
      distance: 100, // 距离底部多远时触发
      disabled: false,
      delay: 200, // 延迟执行时间
      ...binding.modifiers
    }
    
    let loading = false
    let timeoutId = null
    
    const handleScroll = () => {
      if (loading || options.disabled) return
      
      const { scrollTop, scrollHeight, clientHeight } = el
      const distance = parseInt(options.distance) || 100
      
      // 检查是否滚动到接近底部
      if (scrollTop + clientHeight >= scrollHeight - distance) {
        loading = true
        
        // 延迟执行
        if (timeoutId) {
          clearTimeout(timeoutId)
        }
        
        timeoutId = setTimeout(async () => {
          try {
            if (typeof binding.value === 'function') {
              await binding.value()
            }
          } catch (error) {
            console.error('无限滚动加载失败:', error)
          } finally {
            loading = false
          }
        }, options.delay)
      }
    }
    
    // 节流处理
    let throttleTimer = null
    const throttledScroll = () => {
      if (throttleTimer) return
      
      throttleTimer = setTimeout(() => {
        handleScroll()
        throttleTimer = null
      }, 16) // 约60fps
    }
    
    el._infiniteScrollHandler = throttledScroll
    el._infiniteScrollOptions = options
    
    // 添加滚动事件监听
    el.addEventListener('scroll', throttledScroll, { passive: true })
  },
  
  updated(el, binding) {
    // 更新配置
    const options = {
      ...el._infiniteScrollOptions,
      ...binding.modifiers
    }
    el._infiniteScrollOptions = options
  },
  
  unmounted(el) {
    // 清理事件监听器
    if (el._infiniteScrollHandler) {
      el.removeEventListener('scroll', el._infiniteScrollHandler)
      delete el._infiniteScrollHandler
    }
    delete el._infiniteScrollOptions
  }
}

export default infiniteScroll
