/**
 * 图片懒加载自定义指令
 * 当图片进入视口时才开始加载
 */
export const lazyLoad = {
  beforeMount(el, binding) {
    // 创建 IntersectionObserver 实例
    el._observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target
            const src = binding.value || img.dataset.src
            
            if (src) {
              // 创建新的图片对象来预加载
              const newImg = new Image()
              
              newImg.onload = () => {
                // 图片加载成功后设置 src
                img.src = src
                img.classList.remove('lazy-loading')
                img.classList.add('lazy-loaded')
                
                // 触发自定义事件
                img.dispatchEvent(new CustomEvent('lazy-loaded'))
              }
              
              newImg.onerror = () => {
                // 图片加载失败
                img.classList.remove('lazy-loading')
                img.classList.add('lazy-error')
                
                // 设置默认错误图片
                if (binding.arg) {
                  img.src = binding.arg
                }
                
                img.dispatchEvent(new CustomEvent('lazy-error'))
              }
              
              // 开始加载图片
              newImg.src = src
              img.classList.add('lazy-loading')
            }
            
            // 停止观察该元素
            el._observer.unobserve(img)
          }
        })
      },
      {
        rootMargin: '50px', // 提前50px开始加载
        threshold: 0.1
      }
    )
    
    // 开始观察元素
    el._observer.observe(el)
    
    // 添加初始样式类
    el.classList.add('lazy-image')
  },
  
  updated(el, binding) {
    // 如果 binding.value 发生变化，更新 data-src
    if (binding.value) {
      el.dataset.src = binding.value
    }
  },
  
  unmounted(el) {
    // 停止观察并清理
    if (el._observer) {
      el._observer.unobserve(el)
      el._observer.disconnect()
      delete el._observer
    }
  }
}

export default lazyLoad
