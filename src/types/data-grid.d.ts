import { TableProps, TableColumnCtx } from 'element-plus'
import { VNode, Component } from 'vue'

// 列配置类型
export interface DataGridColumn extends Partial<TableColumnCtx<any>> {
  prop: string
  label: string
  type?: 'text' | 'number' | 'date' | 'boolean' | 'custom' | 'action'
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean | 'custom'
  filterable?: boolean
  filterMethod?: (value: any, row: any, column: any) => boolean
  filterMultiple?: boolean
  filterOptions?: Array<{ text: string; value: any }>
  resizable?: boolean
  showOverflowTooltip?: boolean
  align?: 'left' | 'center' | 'right'
  headerAlign?: 'left' | 'center' | 'right'
  className?: string
  labelClassName?: string
  renderHeader?: (column: DataGridColumn) => VNode | string
  renderCell?: (params: { row: any; column: DataGridColumn; $index: number }) => VNode | string
  // 表单配置（用于筛选和编辑）
  formConfig?: FormFieldConfig
}

// 操作列配置
export interface ActionColumnConfig {
  label?: string
  width?: string | number
  fixed?: boolean | 'left' | 'right'
  actions: ActionButtonConfig[]
}

// 操作按钮配置
export interface ActionButtonConfig {
  text: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'
  size?: 'large' | 'default' | 'small'
  icon?: string | Component
  disabled?: boolean | ((row: any) => boolean)
  visible?: boolean | ((row: any) => boolean)
  onClick: (row: any, index: number) => void
}

// 数据源配置
export interface DataSourceConfig {
  // 本地数据
  data?: any[]
  // 远程数据配置
  remote?: {
    url: string
    method?: 'GET' | 'POST'
    params?: Record<string, any>
    headers?: Record<string, string>
    transformRequest?: (params: any) => any
    transformResponse?: (response: any) => DataSourceResponse
  }
  // 分页配置
  pagination?: {
    enabled: boolean
    pageSize: number
    pageSizes?: number[]
    layout?: string
    total?: number
  }
  // 排序配置
  sorting?: {
    enabled: boolean
    defaultSort?: { prop: string; order: 'ascending' | 'descending' }
  }
  // 筛选配置
  filtering?: {
    enabled: boolean
    advanced?: boolean
  }
}

// 数据源响应格式
export interface DataSourceResponse {
  data: any[]
  total: number
  page: number
  pageSize: number
}

// 高级筛选配置
export interface AdvancedFilterConfig {
  enabled: boolean
  fields: FormFieldConfig[]
  layout?: 'inline' | 'vertical'
  collapsible?: boolean
  defaultCollapsed?: boolean
}

// DataGrid 主配置
export interface DataGridConfig {
  columns: DataGridColumn[]
  dataSource: DataSourceConfig
  actionColumn?: ActionColumnConfig
  advancedFilter?: AdvancedFilterConfig
  // Element Plus Table 原生属性
  tableProps?: Partial<TableProps<any>>
  // 事件配置
  events?: {
    onSelectionChange?: (selection: any[]) => void
    onSortChange?: (params: { column: any; prop: string; order: string }) => void
    onFilterChange?: (filters: Record<string, any[]>) => void
    onRowClick?: (row: any, column: any, event: Event) => void
    onRowDblClick?: (row: any, column: any, event: Event) => void
  }
}

// 表单字段配置（用于筛选和编辑）
export interface FormFieldConfig {
  prop: string
  label: string
  type: 'input' | 'select' | 'date' | 'daterange' | 'number' | 'checkbox' | 'radio' | 'custom'
  placeholder?: string
  defaultValue?: any
  required?: boolean
  rules?: any[]
  options?: Array<{ label: string; value: any }>
  disabled?: boolean | ((formData: any) => boolean)
  visible?: boolean | ((formData: any) => boolean)
  span?: number
  component?: Component
  componentProps?: Record<string, any>
  // 联动配置
  dependencies?: string[]
  dependencyLogic?: (formData: any, dependencies: any[]) => Partial<FormFieldConfig>
}

// JSON Schema Form 配置
export interface JsonSchemaFormConfig {
  schema: JsonSchema
  uiSchema?: UiSchema
  formData?: any
  rules?: Record<string, any[]>
  layout?: 'vertical' | 'horizontal' | 'inline'
  labelWidth?: string
  disabled?: boolean
  readonly?: boolean
  onSubmit?: (formData: any) => void
  onReset?: () => void
  onValidate?: (valid: boolean, errors: any) => void
}

// JSON Schema 定义
export interface JsonSchema {
  type: string
  properties: Record<string, JsonSchemaProperty>
  required?: string[]
  title?: string
  description?: string
}

export interface JsonSchemaProperty {
  type: string
  title?: string
  description?: string
  default?: any
  enum?: any[]
  format?: string
  minimum?: number
  maximum?: number
  minLength?: number
  maxLength?: number
  pattern?: string
  items?: JsonSchemaProperty
  properties?: Record<string, JsonSchemaProperty>
  required?: string[]
}

// UI Schema 定义
export interface UiSchema {
  [key: string]: UiSchemaItem
}

export interface UiSchemaItem {
  'ui:widget'?: string
  'ui:options'?: Record<string, any>
  'ui:placeholder'?: string
  'ui:disabled'?: boolean
  'ui:readonly'?: boolean
  'ui:help'?: string
  'ui:order'?: string[]
  'ui:column'?: number
}

// 自定义组件注册
export interface CustomComponent {
  name: string
  component: Component
  props?: string[]
  events?: string[]
}

// DataGrid 实例方法
export interface DataGridInstance {
  // 数据操作
  refresh: () => void
  search: (params?: Record<string, any>) => void
  resetSearch: () => void
  // 选择操作
  clearSelection: () => void
  toggleRowSelection: (row: any, selected?: boolean) => void
  toggleAllSelection: () => void
  getSelectionRows: () => any[]
  // 排序操作
  clearSort: () => void
  sort: (prop: string, order: 'ascending' | 'descending') => void
  // 筛选操作
  clearFilter: (columnKeys?: string[]) => void
  // 表格操作
  scrollTo: (options: { top?: number; left?: number }) => void
  setCurrentRow: (row: any) => void
  // 导出功能
  exportData: (format: 'excel' | 'csv', options?: any) => void
}
