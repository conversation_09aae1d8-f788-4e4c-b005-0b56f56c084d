// 全局类型定义

// 用户相关类型
export interface User {
  id: number
  name: string
  email: string
  avatar?: string
  isActive: boolean
}

// API 响应类型
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

// 分页数据类型
export interface PageData<T = any> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

// 菜单项类型
export interface MenuItem {
  id: string
  title: string
  path?: string
  icon?: string
  children?: MenuItem[]
}
