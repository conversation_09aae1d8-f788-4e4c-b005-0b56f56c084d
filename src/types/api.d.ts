// API related type definitions

// Base API response structure
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  code: number;
}

// Pagination response
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// Request parameters
export interface BaseListParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// User related types
export interface User {
  id: number | string;
  username: string;
  email: string;
  avatar?: string;
  roles: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
  remember?: boolean;
}

export interface UserProfile {
  id: number | string;
  username: string;
  email: string;
  avatar?: string;
  phone?: string;
  bio?: string;
}

// Generic error response
export interface ApiError {
  message: string;
  code: number;
  details?: any;
}

// Request/Response types for common operations
export type CreateUserData = Omit<User, 'id' | 'createdAt' | 'updatedAt'>;
export type UpdateUserData = Partial<Omit<User, 'id' | 'createdAt' | 'updatedAt'>>;
