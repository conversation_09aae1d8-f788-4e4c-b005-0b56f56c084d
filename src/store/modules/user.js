import api from '@/api'

const state = () => ({
  currentUser: null,
  token: localStorage.getItem('token') || null,
  refreshToken: localStorage.getItem('refreshToken') || null,
  userInfo: null,
  permissions: [],
  roles: [],
  isLoading: false,
  error: null
})

const getters = {
  // 是否已登录
  isAuthenticated: state => !!state.token && !!state.currentUser,
  
  // 用户名
  username: state => state.currentUser?.name || state.currentUser?.username || 'Guest',
  
  // 用户头像
  avatar: state => state.currentUser?.avatar || '/default-avatar.png',
  
  // 用户权限
  userPermissions: state => state.permissions || [],
  
  // 用户角色
  userRoles: state => state.roles || [],
  
  // 是否有管理员权限
  isAdmin: state => state.roles?.includes('admin') || false,
  
  // 检查是否有特定权限
  hasPermission: state => permission => {
    if (!permission) return true
    return state.permissions?.includes(permission) || state.permissions?.includes('*')
  },
  
  // 检查是否有特定角色
  hasRole: state => role => {
    if (!role) return true
    return state.roles?.includes(role) || state.roles?.includes('admin')
  }
}

const mutations = {
  SET_LOADING(state, status) {
    state.isLoading = status
  },
  
  SET_ERROR(state, error) {
    state.error = error
  },
  
  SET_TOKEN(state, token) {
    state.token = token
    if (token) {
      localStorage.setItem('token', token)
    } else {
      localStorage.removeItem('token')
    }
  },
  
  SET_REFRESH_TOKEN(state, refreshToken) {
    state.refreshToken = refreshToken
    if (refreshToken) {
      localStorage.setItem('refreshToken', refreshToken)
    } else {
      localStorage.removeItem('refreshToken')
    }
  },
  
  SET_CURRENT_USER(state, user) {
    state.currentUser = user
  },
  
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
  },
  
  SET_PERMISSIONS(state, permissions) {
    state.permissions = permissions || []
  },
  
  SET_ROLES(state, roles) {
    state.roles = roles || []
  },
  
  CLEAR_USER_DATA(state) {
    state.currentUser = null
    state.userInfo = null
    state.permissions = []
    state.roles = []
    state.token = null
    state.refreshToken = null
    state.error = null
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
  }
}

const actions = {
  // 登录
  async login({ commit }, credentials) {
    try {
      commit('SET_LOADING', true)
      commit('SET_ERROR', null)
      
      const response = await api.user.login(credentials)
      const { user, token, refreshToken, permissions, roles } = response
      
      commit('SET_TOKEN', token)
      commit('SET_REFRESH_TOKEN', refreshToken)
      commit('SET_CURRENT_USER', user)
      commit('SET_PERMISSIONS', permissions)
      commit('SET_ROLES', roles)
      
      return user
    } catch (error) {
      commit('SET_ERROR', error.message || '登录失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 登出
  async logout({ commit }) {
    try {
      await api.user.logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      commit('CLEAR_USER_DATA')
    }
  },
  
  // 获取当前用户信息
  async fetchCurrentUser({ commit, state }) {
    if (!state.token) {
      throw new Error('未登录')
    }
    
    try {
      commit('SET_LOADING', true)
      
      const response = await api.user.getCurrentUser()
      const { user, permissions, roles } = response
      
      commit('SET_CURRENT_USER', user)
      commit('SET_PERMISSIONS', permissions)
      commit('SET_ROLES', roles)
      
      return user
    } catch (error) {
      // 如果获取用户信息失败，可能是 token 过期
      if (error.response?.status === 401) {
        commit('CLEAR_USER_DATA')
      }
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 刷新 token
  async refreshToken({ commit, state }) {
    if (!state.refreshToken) {
      throw new Error('没有刷新令牌')
    }
    
    try {
      const response = await api.user.refreshToken(state.refreshToken)
      const { token, refreshToken } = response
      
      commit('SET_TOKEN', token)
      commit('SET_REFRESH_TOKEN', refreshToken)
      
      return token
    } catch (error) {
      commit('CLEAR_USER_DATA')
      throw error
    }
  },
  
  // 更新用户信息
  async updateUserInfo({ commit }, userInfo) {
    try {
      commit('SET_LOADING', true)
      
      const response = await api.user.updateUserInfo(userInfo)
      commit('SET_CURRENT_USER', response)
      
      return response
    } catch (error) {
      commit('SET_ERROR', error.message || '更新用户信息失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 修改密码
  async changePassword({ commit }, { oldPassword, newPassword }) {
    try {
      commit('SET_LOADING', true)
      commit('SET_ERROR', null)
      
      await api.user.changePassword({ oldPassword, newPassword })
      
      return true
    } catch (error) {
      commit('SET_ERROR', error.message || '修改密码失败')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
