const state = () => ({
  // 应用配置
  title: 'Vue3 Element Plus',
  version: '1.0.0',
  
  // 布局配置
  sidebar: {
    opened: !localStorage.getItem('sidebarClosed'),
    withoutAnimation: false
  },
  
  // 主题配置
  theme: {
    mode: localStorage.getItem('theme-mode') || 'light', // light, dark, auto
    primaryColor: localStorage.getItem('theme-primary-color') || '#409EFF',
    layout: localStorage.getItem('theme-layout') || 'default' // default, compact
  },
  
  // 语言配置
  language: localStorage.getItem('language') || 'zh-CN',
  
  // 设备信息
  device: 'desktop', // desktop, mobile, tablet
  
  // 加载状态
  globalLoading: false,
  
  // 面包屑导航
  breadcrumbs: [],
  
  // 标签页
  visitedViews: JSON.parse(localStorage.getItem('visitedViews') || '[]'),
  cachedViews: [],
  
  // 全局设置
  settings: {
    showBreadcrumb: true,
    showTags: true,
    showFooter: true,
    showLogo: true,
    fixedHeader: true,
    sidebarLogo: true,
    tagsView: true
  }
})

const getters = {
  // 侧边栏状态
  sidebarOpened: state => state.sidebar.opened,
  sidebarWithoutAnimation: state => state.sidebar.withoutAnimation,
  
  // 主题相关
  isDarkMode: state => {
    if (state.theme.mode === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    return state.theme.mode === 'dark'
  },
  
  themeMode: state => state.theme.mode,
  primaryColor: state => state.theme.primaryColor,
  
  // 设备类型
  isMobile: state => state.device === 'mobile',
  isDesktop: state => state.device === 'desktop',
  
  // 访问过的视图
  visitedViews: state => state.visitedViews,
  cachedViews: state => state.cachedViews
}

const mutations = {
  // 切换侧边栏
  TOGGLE_SIDEBAR(state, withoutAnimation = false) {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = withoutAnimation
    
    if (state.sidebar.opened) {
      localStorage.removeItem('sidebarClosed')
    } else {
      localStorage.setItem('sidebarClosed', '1')
    }
  },
  
  // 关闭侧边栏
  CLOSE_SIDEBAR(state, withoutAnimation = false) {
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
    localStorage.setItem('sidebarClosed', '1')
  },
  
  // 设置设备类型
  SET_DEVICE(state, device) {
    state.device = device
  },
  
  // 设置主题模式
  SET_THEME_MODE(state, mode) {
    state.theme.mode = mode
    localStorage.setItem('theme-mode', mode)
  },
  
  // 设置主题色
  SET_PRIMARY_COLOR(state, color) {
    state.theme.primaryColor = color
    localStorage.setItem('theme-primary-color', color)
  },
  
  // 设置布局模式
  SET_LAYOUT(state, layout) {
    state.theme.layout = layout
    localStorage.setItem('theme-layout', layout)
  },
  
  // 设置语言
  SET_LANGUAGE(state, language) {
    state.language = language
    localStorage.setItem('language', language)
  },
  
  // 设置全局加载状态
  SET_GLOBAL_LOADING(state, loading) {
    state.globalLoading = loading
  },
  
  // 设置面包屑
  SET_BREADCRUMBS(state, breadcrumbs) {
    state.breadcrumbs = breadcrumbs
  },
  
  // 添加访问过的视图
  ADD_VISITED_VIEW(state, view) {
    if (state.visitedViews.some(v => v.path === view.path)) return
    
    state.visitedViews.push({
      name: view.name,
      path: view.path,
      title: view.meta?.title || view.name,
      meta: view.meta
    })
    
    localStorage.setItem('visitedViews', JSON.stringify(state.visitedViews))
  },
  
  // 添加缓存视图
  ADD_CACHED_VIEW(state, view) {
    if (state.cachedViews.includes(view.name)) return
    if (!view.meta?.noCache) {
      state.cachedViews.push(view.name)
    }
  },
  
  // 删除访问过的视图
  DEL_VISITED_VIEW(state, view) {
    const index = state.visitedViews.findIndex(v => v.path === view.path)
    if (index > -1) {
      state.visitedViews.splice(index, 1)
      localStorage.setItem('visitedViews', JSON.stringify(state.visitedViews))
    }
  },
  
  // 删除缓存视图
  DEL_CACHED_VIEW(state, view) {
    const index = state.cachedViews.indexOf(view.name)
    if (index > -1) {
      state.cachedViews.splice(index, 1)
    }
  },
  
  // 删除其他访问过的视图
  DEL_OTHERS_VISITED_VIEWS(state, view) {
    state.visitedViews = state.visitedViews.filter(v => {
      return v.meta?.affix || v.path === view.path
    })
    localStorage.setItem('visitedViews', JSON.stringify(state.visitedViews))
  },
  
  // 删除其他缓存视图
  DEL_OTHERS_CACHED_VIEWS(state, view) {
    const index = state.cachedViews.indexOf(view.name)
    if (index > -1) {
      state.cachedViews = state.cachedViews.slice(index, index + 1)
    } else {
      state.cachedViews = []
    }
  },
  
  // 删除所有访问过的视图
  DEL_ALL_VISITED_VIEWS(state) {
    const affixTags = state.visitedViews.filter(tag => tag.meta?.affix)
    state.visitedViews = affixTags
    localStorage.setItem('visitedViews', JSON.stringify(state.visitedViews))
  },
  
  // 删除所有缓存视图
  DEL_ALL_CACHED_VIEWS(state) {
    state.cachedViews = []
  },
  
  // 更新设置
  UPDATE_SETTINGS(state, settings) {
    state.settings = { ...state.settings, ...settings }
  }
}

const actions = {
  // 切换侧边栏
  toggleSidebar({ commit }, withoutAnimation) {
    commit('TOGGLE_SIDEBAR', withoutAnimation)
  },
  
  // 关闭侧边栏
  closeSidebar({ commit }, withoutAnimation) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  
  // 切换设备
  toggleDevice({ commit }, device) {
    commit('SET_DEVICE', device)
  },
  
  // 切换主题模式
  toggleThemeMode({ commit, state }) {
    const modes = ['light', 'dark', 'auto']
    const currentIndex = modes.indexOf(state.theme.mode)
    const nextMode = modes[(currentIndex + 1) % modes.length]
    commit('SET_THEME_MODE', nextMode)
  },
  
  // 设置主题
  setTheme({ commit }, { mode, primaryColor, layout }) {
    if (mode) commit('SET_THEME_MODE', mode)
    if (primaryColor) commit('SET_PRIMARY_COLOR', primaryColor)
    if (layout) commit('SET_LAYOUT', layout)
  },
  
  // 设置语言
  setLanguage({ commit }, language) {
    commit('SET_LANGUAGE', language)
  },
  
  // 添加视图
  addView({ commit }, view) {
    commit('ADD_VISITED_VIEW', view)
    commit('ADD_CACHED_VIEW', view)
  },
  
  // 删除视图
  delView({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_VISITED_VIEW', view)
      commit('DEL_CACHED_VIEW', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  
  // 删除其他视图
  delOthersViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_OTHERS_VISITED_VIEWS', view)
      commit('DEL_OTHERS_CACHED_VIEWS', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  
  // 删除所有视图
  delAllViews({ commit, state }) {
    return new Promise(resolve => {
      commit('DEL_ALL_VISITED_VIEWS')
      commit('DEL_ALL_CACHED_VIEWS')
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
