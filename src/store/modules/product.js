import api from '@/api'

const state = () => ({
  // 产品列表
  products: [],
  
  // 产品详情
  currentProduct: null,
  
  // 分页信息
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0
  },
  
  // 筛选条件
  filters: {
    category: null,
    status: null,
    priceRange: null,
    keyword: ''
  },
  
  // 排序
  sort: {
    field: 'createTime',
    order: 'desc'
  },
  
  // 加载状态
  loading: {
    list: false,
    detail: false,
    create: false,
    update: false,
    delete: false
  },
  
  // 错误信息
  error: null,
  
  // 产品分类
  categories: [],
  
  // 产品统计
  statistics: {
    total: 0,
    active: 0,
    inactive: 0,
    draft: 0
  }
})

const getters = {
  // 获取产品列表
  productList: state => state.products,
  
  // 获取当前产品
  currentProduct: state => state.currentProduct,
  
  // 分页信息
  pagination: state => state.pagination,
  
  // 是否有更多数据
  hasMore: state => {
    const { current, pageSize, total } = state.pagination
    return current * pageSize < total
  },
  
  // 筛选后的产品数量
  filteredCount: state => state.products.length,
  
  // 按分类分组的产品
  productsByCategory: state => {
    const grouped = {}
    state.products.forEach(product => {
      const category = product.category || 'uncategorized'
      if (!grouped[category]) {
        grouped[category] = []
      }
      grouped[category].push(product)
    })
    return grouped
  },
  
  // 热门产品（按销量排序）
  popularProducts: state => {
    return [...state.products]
      .sort((a, b) => (b.sales || 0) - (a.sales || 0))
      .slice(0, 10)
  }
}

const mutations = {
  // 设置加载状态
  SET_LOADING(state, { type, status }) {
    if (state.loading.hasOwnProperty(type)) {
      state.loading[type] = status
    }
  },
  
  // 设置错误信息
  SET_ERROR(state, error) {
    state.error = error
  },
  
  // 设置产品列表
  SET_PRODUCTS(state, products) {
    state.products = products
  },
  
  // 添加产品到列表（用于分页加载）
  ADD_PRODUCTS(state, products) {
    state.products.push(...products)
  },
  
  // 设置当前产品
  SET_CURRENT_PRODUCT(state, product) {
    state.currentProduct = product
  },
  
  // 设置分页信息
  SET_PAGINATION(state, pagination) {
    state.pagination = { ...state.pagination, ...pagination }
  },
  
  // 设置筛选条件
  SET_FILTERS(state, filters) {
    state.filters = { ...state.filters, ...filters }
  },
  
  // 设置排序
  SET_SORT(state, sort) {
    state.sort = { ...state.sort, ...sort }
  },
  
  // 添加产品
  ADD_PRODUCT(state, product) {
    state.products.unshift(product)
    state.pagination.total += 1
  },
  
  // 更新产品
  UPDATE_PRODUCT(state, updatedProduct) {
    const index = state.products.findIndex(p => p.id === updatedProduct.id)
    if (index !== -1) {
      state.products.splice(index, 1, updatedProduct)
    }
    
    // 如果是当前产品，也更新当前产品
    if (state.currentProduct && state.currentProduct.id === updatedProduct.id) {
      state.currentProduct = updatedProduct
    }
  },
  
  // 删除产品
  DELETE_PRODUCT(state, productId) {
    const index = state.products.findIndex(p => p.id === productId)
    if (index !== -1) {
      state.products.splice(index, 1)
      state.pagination.total -= 1
    }
    
    // 如果删除的是当前产品，清空当前产品
    if (state.currentProduct && state.currentProduct.id === productId) {
      state.currentProduct = null
    }
  },
  
  // 设置产品分类
  SET_CATEGORIES(state, categories) {
    state.categories = categories
  },
  
  // 设置统计信息
  SET_STATISTICS(state, statistics) {
    state.statistics = { ...state.statistics, ...statistics }
  },
  
  // 重置状态
  RESET_STATE(state) {
    state.products = []
    state.currentProduct = null
    state.pagination = {
      current: 1,
      pageSize: 10,
      total: 0
    }
    state.error = null
  }
}

const actions = {
  // 获取产品列表
  async fetchProducts({ commit, state }, { page = 1, append = false } = {}) {
    try {
      commit('SET_LOADING', { type: 'list', status: true })
      commit('SET_ERROR', null)
      
      const params = {
        page,
        pageSize: state.pagination.pageSize,
        ...state.filters,
        ...state.sort
      }
      
      const response = await api.product.getProducts(params)
      const { data, total, current } = response
      
      if (append) {
        commit('ADD_PRODUCTS', data)
      } else {
        commit('SET_PRODUCTS', data)
      }
      
      commit('SET_PAGINATION', { current, total })
      
      return response
    } catch (error) {
      commit('SET_ERROR', error.message || '获取产品列表失败')
      throw error
    } finally {
      commit('SET_LOADING', { type: 'list', status: false })
    }
  },
  
  // 获取产品详情
  async fetchProductDetail({ commit }, productId) {
    try {
      commit('SET_LOADING', { type: 'detail', status: true })
      commit('SET_ERROR', null)
      
      const product = await api.product.getProductById(productId)
      commit('SET_CURRENT_PRODUCT', product)
      
      return product
    } catch (error) {
      commit('SET_ERROR', error.message || '获取产品详情失败')
      throw error
    } finally {
      commit('SET_LOADING', { type: 'detail', status: false })
    }
  },
  
  // 创建产品
  async createProduct({ commit }, productData) {
    try {
      commit('SET_LOADING', { type: 'create', status: true })
      commit('SET_ERROR', null)
      
      const product = await api.product.createProduct(productData)
      commit('ADD_PRODUCT', product)
      
      return product
    } catch (error) {
      commit('SET_ERROR', error.message || '创建产品失败')
      throw error
    } finally {
      commit('SET_LOADING', { type: 'create', status: false })
    }
  },
  
  // 更新产品
  async updateProduct({ commit }, { id, data }) {
    try {
      commit('SET_LOADING', { type: 'update', status: true })
      commit('SET_ERROR', null)
      
      const product = await api.product.updateProduct(id, data)
      commit('UPDATE_PRODUCT', product)
      
      return product
    } catch (error) {
      commit('SET_ERROR', error.message || '更新产品失败')
      throw error
    } finally {
      commit('SET_LOADING', { type: 'update', status: false })
    }
  },
  
  // 删除产品
  async deleteProduct({ commit }, productId) {
    try {
      commit('SET_LOADING', { type: 'delete', status: true })
      commit('SET_ERROR', null)
      
      await api.product.deleteProduct(productId)
      commit('DELETE_PRODUCT', productId)
      
      return true
    } catch (error) {
      commit('SET_ERROR', error.message || '删除产品失败')
      throw error
    } finally {
      commit('SET_LOADING', { type: 'delete', status: false })
    }
  },
  
  // 设置筛选条件
  setFilters({ commit, dispatch }, filters) {
    commit('SET_FILTERS', filters)
    commit('SET_PAGINATION', { current: 1 })
    return dispatch('fetchProducts')
  },
  
  // 设置排序
  setSort({ commit, dispatch }, sort) {
    commit('SET_SORT', sort)
    commit('SET_PAGINATION', { current: 1 })
    return dispatch('fetchProducts')
  },
  
  // 搜索产品
  async searchProducts({ commit, dispatch }, keyword) {
    commit('SET_FILTERS', { keyword })
    commit('SET_PAGINATION', { current: 1 })
    return dispatch('fetchProducts')
  },
  
  // 加载更多产品
  async loadMoreProducts({ state, dispatch }) {
    const nextPage = state.pagination.current + 1
    return dispatch('fetchProducts', { page: nextPage, append: true })
  },
  
  // 获取产品分类
  async fetchCategories({ commit }) {
    try {
      const categories = await api.product.getCategories()
      commit('SET_CATEGORIES', categories)
      return categories
    } catch (error) {
      commit('SET_ERROR', error.message || '获取产品分类失败')
      throw error
    }
  },
  
  // 获取产品统计
  async fetchStatistics({ commit }) {
    try {
      const statistics = await api.product.getStatistics()
      commit('SET_STATISTICS', statistics)
      return statistics
    } catch (error) {
      commit('SET_ERROR', error.message || '获取产品统计失败')
      throw error
    }
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
