<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { CountTo } from 'vue3-count-to'

// 定义兼容 vue-count-to 的属性接口
interface GalaxyCountToProps {
  // vue-count-to 的原始属性（camelCase）
  startVal?: number
  endVal?: number
  duration?: number
  autoplay?: boolean
  decimals?: number
  decimal?: string
  separator?: string
  prefix?: string
  suffix?: string
  useEasing?: boolean
  easingFn?: (progress: number, localStartVal: number, intervalVal: number, localDuration: number) => number

  // 同时支持 kebab-case 属性
  'start-val'?: number
  'end-val'?: number
  'use-easing'?: boolean
  'easing-fn'?: (progress: number, localStartVal: number, intervalVal: number, localDuration: number) => number
}

// 定义事件接口 - 兼容 vue-count-to 的事件名
interface GalaxyCountToEmits {
  // vue-count-to 的原始事件名
  (e: 'mountedCallback'): void
  (e: 'callback'): void
  // 通用事件名
  (e: 'mounted'): void
  (e: 'finished'): void
}

// 定义 props 默认值
const props = withDefaults(defineProps<GalaxyCountToProps>(), {
  startVal: 0,
  endVal: 2017,
  duration: 3000,
  autoplay: true,
  decimals: 0,
  decimal: '.',
  separator: ',',
  prefix: '',
  suffix: '',
  useEasing: true
})

// 定义事件
const emit = defineEmits<GalaxyCountToEmits>()

// 获取 CountTo 组件的引用
const countToRef = ref<InstanceType<typeof CountTo> | null>(null)

// 暂停/恢复状态管理
const isPaused = ref(false)
const isStarted = ref(false)
const pausedTime = ref<number>(0) // 暂停时已经经过的时间
const totalPausedTime = ref<number>(0) // 总暂停时间
const animationStartTime = ref<number>(0) // 动画开始时间

// 自定义计数器状态
const isUsingCustomCounter = ref(false)
const currentValue = ref<number>(0)
const animationId = ref<number | null>(null)

// 计算属性 - 将属性转换为 vue3-count-to 期望的格式
const normalizedProps = computed(() => ({
  startVal: props.startVal ?? props['start-val'] ?? 0,
  endVal: props.endVal ?? props['end-val'] ?? 2017,
  duration: props.duration ?? 3000,
  autoplay: props.autoplay ?? true,
  decimals: props.decimals ?? 0,
  decimal: props.decimal ?? '.',
  separator: props.separator ?? ',',
  prefix: props.prefix ?? '',
  suffix: props.suffix ?? '',
  useEasing: props.useEasing ?? props['use-easing'] ?? true,
  easingFn: props.easingFn ?? props['easing-fn']
}))

// 自定义缓动函数
const defaultEasingFn = (progress: number, startVal: number, intervalVal: number, duration: number) => {
  return intervalVal * (-Math.pow(2, -10 * progress / duration) + 1) + startVal
}

// 格式化数字
const formatNumber = (num: number): string => {
  const data = num.toFixed(normalizedProps.value.decimals)
  const [integer, decimal] = data.split('.')

  let integerFormatted = integer
  if (normalizedProps.value.separator) {
    integerFormatted = integer.replace(/(\d)(?=(\d{3})+$)/g, `$1${normalizedProps.value.separator}`)
  }

  let result = normalizedProps.value.decimals ? `${integerFormatted}${normalizedProps.value.decimal}${decimal}` : integerFormatted

  if (normalizedProps.value.prefix) result = normalizedProps.value.prefix + result
  if (normalizedProps.value.suffix) result = result + normalizedProps.value.suffix

  return result
}

// 自定义动画函数
const animate = (timestamp: number) => {
  if (!isStarted.value || isPaused.value) return

  if (!animationStartTime.value) {
    animationStartTime.value = timestamp - totalPausedTime.value
  }

  const elapsed = timestamp - animationStartTime.value
  const progress = Math.min(elapsed / normalizedProps.value.duration, 1)

  const startVal = normalizedProps.value.startVal
  const endVal = normalizedProps.value.endVal
  const intervalVal = endVal - startVal

  if (normalizedProps.value.useEasing && normalizedProps.value.easingFn) {
    currentValue.value = normalizedProps.value.easingFn(progress, startVal, intervalVal, normalizedProps.value.duration)
  } else if (normalizedProps.value.useEasing) {
    currentValue.value = defaultEasingFn(elapsed, startVal, intervalVal, normalizedProps.value.duration)
  } else {
    currentValue.value = startVal + intervalVal * progress
  }

  // 确保不超出目标值
  if (intervalVal > 0) {
    currentValue.value = Math.min(currentValue.value, endVal)
  } else {
    currentValue.value = Math.max(currentValue.value, endVal)
  }

  if (progress < 1) {
    animationId.value = requestAnimationFrame(animate)
  } else {
    currentValue.value = endVal
    isStarted.value = false
    emit('finished')
    emit('callback')
  }
}

// 事件处理器
const handleStart = () => {
  emit('mounted')
  emit('mountedCallback')
}

// 暴露方法给父组件
const start = () => {
  if (isPaused.value) {
    // 如果是从暂停状态恢复，使用自定义计数器
    isUsingCustomCounter.value = true
    isPaused.value = false
    isStarted.value = true
    animationStartTime.value = 0
    animationId.value = requestAnimationFrame(animate)
  } else {
    // 全新开始
    if (normalizedProps.value.autoplay && countToRef.value) {
      // 使用 vue3-count-to 的内置功能
      isUsingCustomCounter.value = false
      if (typeof countToRef.value.start === 'function') {
        countToRef.value.start()
      }
    } else {
      // 使用自定义计数器
      isUsingCustomCounter.value = true
      currentValue.value = normalizedProps.value.startVal
      animationStartTime.value = 0
      totalPausedTime.value = 0
    }
    isStarted.value = true
    isPaused.value = false
    handleStart()

    if (isUsingCustomCounter.value) {
      animationId.value = requestAnimationFrame(animate)
    }
  }
}

const pause = () => {
  if (!isStarted.value) return

  isPaused.value = true
  pausedTime.value = performance.now()

  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
    animationId.value = null
  }

  // 如果正在使用 vue3-count-to，切换到自定义计数器
  if (!isUsingCustomCounter.value && countToRef.value) {
    isUsingCustomCounter.value = true
    if (typeof countToRef.value.pause === 'function') {
      countToRef.value.pause()
    }
  }
}

const resume = () => {
  if (!isPaused.value) return

  isPaused.value = false

  if (pausedTime.value) {
    totalPausedTime.value += performance.now() - pausedTime.value
    pausedTime.value = 0
  }

  if (isStarted.value) {
    isUsingCustomCounter.value = true
    animationId.value = requestAnimationFrame(animate)
  }
}

const reset = () => {
  isPaused.value = false
  isStarted.value = false
  isUsingCustomCounter.value = false
  currentValue.value = normalizedProps.value.startVal
  pausedTime.value = 0
  totalPausedTime.value = 0
  animationStartTime.value = 0

  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
    animationId.value = null
  }

  if (countToRef.value && typeof countToRef.value.reset === 'function') {
    countToRef.value.reset()
  }
}

const restart = () => {
  reset()
  setTimeout(() => {
    start()
  }, 50)
}

const pauseResume = () => {
  if (isPaused.value) {
    resume()
  } else {
    pause()
  }
}

// 组件挂载时初始化
onMounted(() => {
  currentValue.value = normalizedProps.value.startVal
  if (normalizedProps.value.autoplay) {
    handleStart()
  }
})

// 组件卸载时清理
onUnmounted(() => {
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
  }
})

// 暴露所有方法
defineExpose({
  start,
  pause,
  resume,
  reset,
  restart,
  pauseResume
})
</script>

<template>
  <!-- 条件渲染：使用自定义计数器时显示格式化的值，否则使用 vue3-count-to -->
  <span v-if="isUsingCustomCounter">{{ formatNumber(currentValue) }}</span>
  <CountTo
    v-else
    ref="countToRef"
    :startVal="normalizedProps.startVal"
    :endVal="normalizedProps.endVal"
    :duration="normalizedProps.duration"
    :autoplay="normalizedProps.autoplay"
    :decimals="normalizedProps.decimals"
    :decimal="normalizedProps.decimal"
    :separator="normalizedProps.separator"
    :prefix="normalizedProps.prefix"
    :suffix="normalizedProps.suffix"
    :useEasing="normalizedProps.useEasing"
    :easingFn="normalizedProps.easingFn"
  />
</template>

<style scoped>
span {
  display: inline-block;
}
</style>
