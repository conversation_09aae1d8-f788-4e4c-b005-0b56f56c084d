// =================================================================
// Galaxy Design System v4.0 - New Typography System
// =================================================================
@use 'sass:map';

:root {
  // ===== FONT FAMILIES =====
  --font-family-chinese: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Source Han Sans CN',
    'WenQuanYi Micro Hei', sans-serif;
  --font-family-english: 'Inter', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
  --font-family-numeric: 'SF Pro Display', 'Inter', 'Roboto Mono', 'Monaco', monospace;
  --font-family-special-header: 'FZDaBiaoSong-B05S', 'STSong', 'SimSun', serif;
  --font-family-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
  --font-family-display: 'Inter Display', 'SF Pro Display', sans-serif;

  // ===== 字号/行高 - 根据新设计规范 =====
  --font-size-page-title: 1.5rem; // 24px - 页面标题
  --line-height-page-title: 2.0625rem; // 33px

  --font-size-h1: 1.25rem; // 20px - 一级标题
  --line-height-h1: 1.75rem; // 28px

  --font-size-h2: 1.125rem; // 18px - 二级标题
  --line-height-h2: 1.5625rem; // 25px

  --font-size-body: 1rem; // 16px - 正文文字
  --line-height-body: 1.375rem; // 22px

  --font-size-content: 0.875rem; // 14px - 内容文字
  --line-height-content: 1.25rem; // 20px

  --font-size-small: 0.75rem; // 12px - 内容文字
  --line-height-small: 1.0625rem; // 17px

  // ===== 兼容性字号 - 保持现有系统 =====
  --font-size-2xs: 0.625rem; // 10px - Micro text
  --font-size-xs: var(--font-size-small); // 12px - Small labels
  --font-size-sm: 0.8125rem; // 13px - Small text
  --font-size-base: var(--font-size-content); // 14px - Body text
  --font-size-md: 0.9375rem; // 15px - Medium text
  --font-size-lg: var(--font-size-body); // 16px - Large text
  --font-size-xl: var(--font-size-h2); // 18px - Large headings
  --font-size-2xl: var(--font-size-h1); // 20px - Section headings
  --font-size-3xl: var(--font-size-page-title); // 24px - Page headings
  --font-size-4xl: 1.875rem; // 30px - Large headings
  --font-size-5xl: 2.25rem; // 36px - Display headings
  --font-size-6xl: 3rem; // 48px - Hero headings
  --font-size-7xl: 3.75rem; // 60px - Large display
  --font-size-8xl: 4.5rem; // 72px - Extra large display
  --font-size-9xl: 6rem; // 96px - Massive display

  // ===== 兼容性行高 =====
  --line-height-2xs: 0.875rem; // 14px
  --line-height-xs: var(--line-height-small); // 17px
  --line-height-sm: 1.1875rem; // 19px
  --line-height-base: var(--line-height-content); // 20px
  --line-height-md: 1.375rem; // 22px
  --line-height-lg: var(--line-height-body); // 22px
  --line-height-xl: var(--line-height-h2); // 25px
  --line-height-2xl: var(--line-height-h1); // 28px
  --line-height-3xl: var(--line-height-page-title); // 33px
  --line-height-4xl: 2.375rem; // 38px
  --line-height-5xl: 2.75rem; // 44px
  --line-height-6xl: 3.5rem; // 56px
  --line-height-7xl: 4.25rem; // 68px
  --line-height-8xl: 5rem; // 80px
  --line-height-9xl: 6.5rem; // 104px

  // ===== FONT WEIGHTS =====
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  // ===== LETTER SPACING =====
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;

  // ===== TEXT DECORATION =====
  --text-decoration-thickness: 1px;
  --text-underline-offset: 2px;
}

// ===== TYPOGRAPHY SCALE MAP =====
$typography-scale: (
  // 新设计规范的字体尺寸
  'page-title':
    (
      font-size: var(--font-size-page-title),
      line-height: var(--line-height-page-title),
    ),
  'h1': (
    font-size: var(--font-size-h1),
    line-height: var(--line-height-h1),
  ),
  'h2': (
    font-size: var(--font-size-h2),
    line-height: var(--line-height-h2),
  ),
  'body': (
    font-size: var(--font-size-body),
    line-height: var(--line-height-body),
  ),
  'content': (
    font-size: var(--font-size-content),
    line-height: var(--line-height-content),
  ),
  'small': (
    font-size: var(--font-size-small),
    line-height: var(--line-height-small),
  ),
  // 兼容性尺寸
  '2xs':
    (
      font-size: var(--font-size-2xs),
      line-height: var(--line-height-2xs),
    ),
  'xs': (
    font-size: var(--font-size-xs),
    line-height: var(--line-height-xs),
  ),
  'sm': (
    font-size: var(--font-size-sm),
    line-height: var(--line-height-sm),
  ),
  'base': (
    font-size: var(--font-size-base),
    line-height: var(--line-height-base),
  ),
  'md': (
    font-size: var(--font-size-md),
    line-height: var(--line-height-md),
  ),
  'lg': (
    font-size: var(--font-size-lg),
    line-height: var(--line-height-lg),
  ),
  'xl': (
    font-size: var(--font-size-xl),
    line-height: var(--line-height-xl),
  ),
  '2xl': (
    font-size: var(--font-size-2xl),
    line-height: var(--line-height-2xl),
  ),
  '3xl': (
    font-size: var(--font-size-3xl),
    line-height: var(--line-height-3xl),
  ),
  '4xl': (
    font-size: var(--font-size-4xl),
    line-height: var(--line-height-4xl),
  ),
  '5xl': (
    font-size: var(--font-size-5xl),
    line-height: var(--line-height-5xl),
  ),
  '6xl': (
    font-size: var(--font-size-6xl),
    line-height: var(--line-height-6xl),
  ),
  '7xl': (
    font-size: var(--font-size-7xl),
    line-height: var(--line-height-7xl),
  ),
  '8xl': (
    font-size: var(--font-size-8xl),
    line-height: var(--line-height-8xl),
  ),
  '9xl': (
    font-size: var(--font-size-9xl),
    line-height: var(--line-height-9xl),
  )
);

// ===== TYPOGRAPHY MIXINS =====

@mixin text-style($size: 'base', $weight: 'normal', $family: 'chinese') {
  $scale: map.get($typography-scale, $size);

  font-size: map.get($scale, font-size);
  line-height: map.get($scale, line-height);
  font-weight: var(--font-weight-#{$weight});

  @if $family == 'chinese' {
    font-family: var(--font-family-chinese);
  } @else if $family == 'english' {
    font-family: var(--font-family-english);
  } @else if $family == 'numeric' {
    font-family: var(--font-family-numeric);
  } @else if $family == 'mono' {
    font-family: var(--font-family-mono);
  } @else if $family == 'display' {
    font-family: var(--font-family-display);
  } @else if $family == 'special' {
    font-family: var(--font-family-special-header);
  }
}

// ===== 新设计规范 MIXINS =====
@mixin page-title {
  @include text-style('page-title', 'normal', 'chinese');
  letter-spacing: var(--letter-spacing-normal);
}

@mixin heading-1 {
  @include text-style('h1', 'normal', 'chinese');
  letter-spacing: var(--letter-spacing-normal);
}

@mixin heading-2 {
  @include text-style('h2', 'normal', 'chinese');
  letter-spacing: var(--letter-spacing-normal);
}

@mixin body-text {
  @include text-style('body', 'normal', 'chinese');
  letter-spacing: var(--letter-spacing-normal);
}

@mixin content-text {
  @include text-style('content', 'normal', 'chinese');
  letter-spacing: var(--letter-spacing-normal);
}

@mixin small-text {
  @include text-style('small', 'normal', 'chinese');
  letter-spacing: var(--letter-spacing-normal);
}

// ===== 兼容性 HEADING MIXINS =====
@mixin heading-3 {
  @include text-style('4xl', 'semibold', 'display');
  letter-spacing: var(--letter-spacing-normal);
}

@mixin heading-4 {
  @include text-style('3xl', 'semibold', 'chinese');
  letter-spacing: var(--letter-spacing-normal);
}

@mixin heading-5 {
  @include text-style('2xl', 'semibold', 'chinese');
  letter-spacing: var(--letter-spacing-normal);
}

@mixin heading-6 {
  @include text-style('xl', 'semibold', 'chinese');
  letter-spacing: var(--letter-spacing-normal);
}

// ===== 兼容性 BODY TEXT MIXINS =====
@mixin body-large {
  @include text-style('lg', 'normal', 'chinese');
}

@mixin body-base {
  @include text-style('base', 'normal', 'chinese');
}

@mixin body-small {
  @include text-style('sm', 'normal', 'chinese');
}

@mixin caption {
  @include text-style('xs', 'medium', 'chinese');
  letter-spacing: var(--letter-spacing-wide);
  text-transform: uppercase;
}

// ===== SPECIAL TEXT STYLES =====
@mixin text-gradient($gradient) {
  background: #{$gradient};
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@mixin text-shadow-sm {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

@mixin text-shadow-md {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@mixin text-shadow-lg {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08);
}
