// =================================================================
// Galaxy Design System v3.0 - Gradient Components
// =================================================================

// Text gradient mixin
@mixin text-gradient($gradient) {
  background: $gradient;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

// Gradient background utilities
.gradient-bg {
  &--blue-deep {
    background: var(--gradient-blue-deep);
  }

  &--red-deep {
    background: var(--gradient-red-deep);
  }

  &--green-deep {
    background: var(--gradient-green-deep);
  }

  &--orange-deep {
    background: var(--gradient-orange-deep);
  }

  &--purple-deep {
    background: var(--gradient-purple-deep);
  }

  &--teal-deep {
    background: var(--gradient-teal-deep);
  }

  &--blue-light {
    background: var(--gradient-blue-light);
  }

  &--red-light {
    background: var(--gradient-red-light);
  }

  &--green-light {
    background: var(--gradient-green-light);
  }

  &--orange-light {
    background: var(--gradient-orange-light);
  }

  &--purple-light {
    background: var(--gradient-purple-light);
  }

  &--teal-light {
    background: var(--gradient-teal-light);
  }

  &--gold-light {
    background: var(--gradient-gold-light);
  }

  &--gray-light {
    background: var(--gradient-gray-light);
  }

  // Brand gradients
  &--galaxy-red {
    background: var(--gradient-galaxy-red);
  }

  &--galaxy-blue {
    background: var(--gradient-galaxy-blue);
  }

  &--wealth-gold {
    background: var(--gradient-wealth-gold);
  }

  &--tech-blue {
    background: var(--gradient-tech-blue);
  }

  &--vitality-orange {
    background: var(--gradient-vitality-orange);
  }

  &--nature-green {
    background: var(--gradient-nature-green);
  }

  &--royal-purple {
    background: var(--gradient-royal-purple);
  }

  &--ocean-teal {
    background: var(--gradient-ocean-teal);
  }

  // Rainbow gradients
  &--rainbow-warm {
    background: var(--gradient-rainbow-warm);
  }

  &--rainbow-cool {
    background: var(--gradient-rainbow-cool);
  }

  &--sunset {
    background: var(--gradient-sunset);
  }

  &--ocean {
    background: var(--gradient-ocean);
  }

  &--forest {
    background: var(--gradient-forest);
  }
}

// Gradient text utilities
.gradient-text {
  @include text-gradient(var(--gradient-galaxy-blue));

  &--blue {
    @include text-gradient(var(--gradient-blue-deep));
  }

  &--red {
    @include text-gradient(var(--gradient-red-deep));
  }

  &--green {
    @include text-gradient(var(--gradient-green-deep));
  }

  &--orange {
    @include text-gradient(var(--gradient-orange-deep));
  }

  &--purple {
    @include text-gradient(var(--gradient-purple-deep));
  }

  &--teal {
    @include text-gradient(var(--gradient-teal-deep));
  }

  &--galaxy-red {
    @include text-gradient(var(--gradient-galaxy-red));
  }

  &--galaxy-blue {
    @include text-gradient(var(--gradient-galaxy-blue));
  }

  &--wealth-gold {
    @include text-gradient(var(--gradient-wealth-gold));
  }

  &--rainbow-warm {
    @include text-gradient(var(--gradient-rainbow-warm));
  }

  &--rainbow-cool {
    @include text-gradient(var(--gradient-rainbow-cool));
  }

  &--sunset {
    @include text-gradient(var(--gradient-sunset));
  }

  &--ocean {
    @include text-gradient(var(--gradient-ocean));
  }

  &--forest {
    @include text-gradient(var(--gradient-forest));
  }
}

// Gradient border utilities
.gradient-border {
  position: relative;
  background: var(--color-by1);

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: var(--gradient-galaxy-blue);
    border-radius: inherit;
    mask: linear-gradient(to bottom, #fff 0%, #fff 100%) content-box,
    linear-gradient(to bottom, #fff 0%, #fff 100%);
    mask-composite: exclude;
  }

  &--blue::before {
    background: var(--gradient-blue-deep);
  }

  &--red::before {
    background: var(--gradient-red-deep);
  }

  &--green::before {
    background: var(--gradient-green-deep);
  }

  &--orange::before {
    background: var(--gradient-orange-deep);
  }

  &--purple::before {
    background: var(--gradient-purple-deep);
  }

  &--teal::before {
    background: var(--gradient-teal-deep);
  }

  &--rainbow-warm::before {
    background: var(--gradient-rainbow-warm);
  }

  &--rainbow-cool::before {
    background: var(--gradient-rainbow-cool);
  }
}

// Gradient cards
.gradient-card {
  position: relative;
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  color: var(--color-by1);
  overflow: hidden;
  background: var(--gradient-galaxy-blue);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-base);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
  }

  h4,
  h5,
  h6 {
    color: inherit;
    margin: 0 0 var(--space-3) 0;
  }

  p {
    color: inherit;
    opacity: 0.9;
    margin: 0;
  }

  &--blue {
    background: var(--gradient-blue-deep);
  }

  &--red {
    background: var(--gradient-red-deep);
  }

  &--green {
    background: var(--gradient-green-deep);
  }

  &--orange {
    background: var(--gradient-orange-deep);
  }

  &--purple {
    background: var(--gradient-purple-deep);
  }

  &--teal {
    background: var(--gradient-teal-deep);
  }

  &--light {
    color: var(--color-by8);

    &.gradient-card--blue {
      background: var(--gradient-blue-light);
    }

    &.gradient-card--red {
      background: var(--gradient-red-light);
    }

    &.gradient-card--green {
      background: var(--gradient-green-light);
    }

    &.gradient-card--orange {
      background: var(--gradient-orange-light);
    }

    &.gradient-card--purple {
      background: var(--gradient-purple-light);
    }

    &.gradient-card--teal {
      background: var(--gradient-teal-light);
    }

    &.gradient-card--gold {
      background: var(--gradient-gold-light);
    }

    &.gradient-card--gray {
      background: var(--gradient-gray-light);
    }
  }
}
