// =================================================================
// Galaxy Design System v3.0 - Status Components
// =================================================================

// Status indicators
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  
  &::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: currentColor;
  }
  
  // Status variants
  &--in-progress {
    color: var(--color-status-in-progress);
    background-color: var(--color-bu2);
  }
  
  &--incomplete {
    color: var(--color-status-incomplete);
    background-color: var(--color-by2);
  }
  
  &--complete {
    color: var(--color-status-complete);
    background-color: var(--color-gn2);
  }
  
  &--cancelled {
    color: var(--color-status-cancelled);
    background-color: var(--color-rd2);
  }
  
  &--pending {
    color: var(--color-status-pending);
    background-color: var(--color-oe2);
  }
  
  &--draft {
    color: var(--color-status-draft);
    background-color: var(--color-pu2);
  }
  
  // Sizes
  &--sm {
    padding: var(--space-1) var(--space-2);
    font-size: 0.6875rem;
    
    &::before {
      width: 4px;
      height: 4px;
    }
  }
  
  &--lg {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-sm);
    
    &::before {
      width: 8px;
      height: 8px;
    }
  }
}

// Transaction status
.transaction-status {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  
  &::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: currentColor;
  }
  
  &--buy {
    color: var(--color-transaction-buy);
    background-color: var(--color-rd2);
    border: 1px solid var(--color-rd4);
  }
  
  &--sell {
    color: var(--color-transaction-sell);
    background-color: var(--color-bu2);
    border: 1px solid var(--color-bu4);
  }
  
  &--cancel {
    color: var(--color-transaction-cancel);
    background-color: var(--color-by2);
    border: 1px solid var(--color-by4);
  }
  
  &--hold {
    color: var(--color-transaction-hold);
    background-color: var(--color-oe2);
    border: 1px solid var(--color-oe4);
  }
}

// Progress indicators
.progress-ring {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  &__circle {
    transform: rotate(-90deg);
  }
  
  &__background {
    fill: none;
    stroke: var(--color-by3);
    stroke-width: 4;
  }
  
  &__progress {
    fill: none;
    stroke: var(--color-blue-main);
    stroke-width: 4;
    stroke-linecap: round;
    transition: stroke-dashoffset var(--transition-slow);
  }
  
  &__text {
    position: absolute;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--color-by8);
  }
  
  // Color variants
  &--success .progress-ring__progress {
    stroke: var(--color-green-main);
  }
  
  &--warning .progress-ring__progress {
    stroke: var(--color-orange-main);
  }
  
  &--danger .progress-ring__progress {
    stroke: var(--color-red-main);
  }
  
  // Sizes
  &--sm {
    width: 32px;
    height: 32px;
    
    .progress-ring__text {
      font-size: var(--font-size-xs);
    }
  }
  
  &--md {
    width: 48px;
    height: 48px;
  }
  
  &--lg {
    width: 64px;
    height: 64px;
    
    .progress-ring__text {
      font-size: var(--font-size-lg);
    }
  }
}

// Loading states
.loading-skeleton {
  background: linear-gradient(90deg, var(--color-by2) 25%, var(--color-by3) 50%, var(--color-by2) 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
  border-radius: var(--radius-base);
  
  &--text {
    height: 1em;
    margin: 0.25em 0;
  }
  
  &--title {
    height: 1.5em;
    width: 60%;
    margin: 0.5em 0;
  }
  
  &--paragraph {
    height: 1em;
    margin: 0.25em 0;
    
    &:last-child {
      width: 80%;
    }
  }
  
  &--avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
  
  &--button {
    height: 32px;
    width: 80px;
    border-radius: var(--radius-lg);
  }
  
  &--card {
    height: 200px;
    border-radius: var(--radius-xl);
  }
}

@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// Pulse animation for loading states
.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// Heartbeat animation for important status
.heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite both;
}

@keyframes heartbeat {
  from {
    transform: scale(1);
    transform-origin: center center;
    animation-timing-function: ease-out;
  }
  10% {
    transform: scale(0.91);
    animation-timing-function: ease-in;
  }
  17% {
    transform: scale(0.98);
    animation-timing-function: ease-out;
  }
  33% {
    transform: scale(0.87);
    animation-timing-function: ease-in;
  }
  45% {
    transform: scale(1);
    animation-timing-function: ease-out;
  }
}
