// =================================================================
// Galaxy Design System v3.0 - Element Plus Theme Override
// =================================================================

// https://github.com/element-plus/element-plus/blob/dev/packages/theme-chalk/src/common/var.scss

// Import our design tokens first
@use '../abstracts/colors';
@use '../base/typography';
@use '../base/spacing';

// Element Plus CSS Variables Override
:root {
  // ===== PRIMARY COLORS =====
  --el-color-primary: var(--color-blue-main);
  --el-color-primary-light-3: var(--color-bu5);
  --el-color-primary-light-5: var(--color-bu4);
  --el-color-primary-light-7: var(--color-bu3);
  --el-color-primary-light-8: var(--color-bu2);
  --el-color-primary-light-9: var(--color-bu1);
  --el-color-primary-dark-2: var(--color-bu8);

  // ===== SUCCESS COLORS =====
  --el-color-success: var(--color-green-main);
  --el-color-success-light-3: var(--color-gn5);
  --el-color-success-light-5: var(--color-gn4);
  --el-color-success-light-7: var(--color-gn3);
  --el-color-success-light-8: var(--color-gn2);
  --el-color-success-light-9: var(--color-gn1);
  --el-color-success-dark-2: var(--color-gn8);

  // ===== WARNING COLORS =====
  --el-color-warning: var(--color-orange-main);
  --el-color-warning-light-3: var(--color-oe5);
  --el-color-warning-light-5: var(--color-oe4);
  --el-color-warning-light-7: var(--color-oe3);
  --el-color-warning-light-8: var(--color-oe2);
  --el-color-warning-light-9: var(--color-oe1);
  --el-color-warning-dark-2: var(--color-oe8);

  // ===== DANGER COLORS =====
  --el-color-danger: var(--color-red-main);
  --el-color-danger-light-3: var(--color-rd5);
  --el-color-danger-light-5: var(--color-rd4);
  --el-color-danger-light-7: var(--color-rd3);
  --el-color-danger-light-8: var(--color-rd2);
  --el-color-danger-light-9: var(--color-rd1);
  --el-color-danger-dark-2: var(--color-rd8);

  // ===== INFO COLORS =====
  --el-color-info: var(--color-by6);
  --el-color-info-light-3: var(--color-by5);
  --el-color-info-light-5: var(--color-by4);
  --el-color-info-light-7: var(--color-by3);
  --el-color-info-light-8: var(--color-by2);
  --el-color-info-light-9: var(--color-by1);
  --el-color-info-dark-2: var(--color-by7);

  // ===== TEXT COLORS =====
  --el-text-color-primary: var(--color-by8);
  --el-text-color-regular: var(--color-by7);
  --el-text-color-secondary: var(--color-by6);
  --el-text-color-placeholder: var(--color-by5);
  --el-text-color-disabled: var(--color-by5);

  // ===== BORDER COLORS =====
  --el-border-color: var(--color-by4);
  --el-border-color-light: var(--color-by3);
  --el-border-color-lighter: var(--color-by3);
  --el-border-color-extra-light: var(--color-by2);
  --el-border-color-dark: var(--color-by6);
  --el-border-color-darker: var(--color-by7);

  // ===== FILL COLORS =====
  --el-fill-color: var(--color-by2);
  --el-fill-color-light: var(--color-by2);
  --el-fill-color-lighter: var(--color-by1);
  --el-fill-color-extra-light: var(--color-by1);
  --el-fill-color-dark: var(--color-by3);
  --el-fill-color-darker: var(--color-by4);
  --el-fill-color-blank: var(--color-by1);

  // ===== BACKGROUND COLORS =====
  --el-bg-color: var(--color-by1);
  --el-bg-color-page: var(--color-bg);
  --el-bg-color-overlay: var(--color-by1);

  // ===== TYPOGRAPHY =====
  --el-font-family: var(--font-family-chinese);
  --el-font-size-extra-large: var(--font-size-xl);
  --el-font-size-large: var(--font-size-lg);
  --el-font-size-medium: var(--font-size-base);
  --el-font-size-base: var(--font-size-base);
  --el-font-size-small: var(--font-size-sm);
  --el-font-size-extra-small: var(--font-size-xs);

  // ===== SPACING =====
  --el-border-radius-base: var(--radius-lg);
  --el-border-radius-small: var(--radius-md);
  --el-border-radius-round: var(--radius-full);
  --el-border-radius-circle: var(--radius-full);

  // ===== SHADOWS =====
  --el-box-shadow: var(--shadow-base);
  --el-box-shadow-light: var(--shadow-sm);
  --el-box-shadow-dark: var(--shadow-lg);

  // ===== TRANSITIONS =====
  --el-transition-duration: var(--transition-base);
  --el-transition-duration-fast: var(--transition-fast);

  // ===== COMPONENT SPECIFIC =====
  --el-component-size-large: 40px;
  --el-component-size: 32px;
  --el-component-size-small: 24px;
}

// Dark mode overrides for Element Plus
@media (prefers-color-scheme: dark) {
  :root {
    --el-text-color-primary: var(--color-by8);
    --el-text-color-regular: var(--color-by7);
    --el-text-color-secondary: var(--color-by6);
    --el-text-color-placeholder: var(--color-by5);
    --el-text-color-disabled: var(--color-by5);

    --el-border-color: var(--color-by4);
    --el-border-color-light: var(--color-by3);
    --el-border-color-lighter: var(--color-by3);
    --el-border-color-extra-light: var(--color-by2);

    --el-fill-color: var(--color-by2);
    --el-fill-color-light: var(--color-by2);
    --el-fill-color-lighter: var(--color-by1);
    --el-fill-color-extra-light: var(--color-by1);

    --el-bg-color: var(--color-by1);
    --el-bg-color-page: var(--color-bg);
    --el-bg-color-overlay: var(--color-by1);
  }
}

// ===== COMPONENT CUSTOMIZATIONS =====

// Button customizations
.el-button {
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);

  &--large {
    padding: var(--space-4) var(--space-6);
    font-size: var(--font-size-lg);
  }

  &--default {
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-base);
  }

  &--small {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-sm);
    border-radius: var(--radius-md);
  }
}

// Card customizations
.el-card {
  border-radius: var(--radius-xl);
  border: 1px solid var(--color-by3);
  box-shadow: var(--shadow-sm);

  .el-card__header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--color-by3);
  }

  .el-card__body {
    padding: var(--space-6);
  }
}

// Tag customizations
.el-tag {
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-semibold);

  &--large {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-sm);
  }

  &--default {
    padding: var(--space-1) var(--space-3);
    font-size: var(--font-size-xs);
  }

  &--small {
    padding: var(--space-1) var(--space-2);
    font-size: 0.6875rem;
    border-radius: var(--radius-base);
  }
}

// Input customizations
.el-input {
  .el-input__inner {
    border-radius: var(--radius-lg);
    font-family: var(--font-family-chinese);
  }
}

// Select customizations
.el-select {
  .el-input__inner {
    border-radius: var(--radius-lg);
  }
}

// Dialog customizations
.el-dialog {
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
}

// Message customizations
.el-message {
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
}

// Notification customizations
.el-notification {
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
}

// Table customizations
.el-table {
  border-radius: var(--radius-lg);

  th {
    background-color: var(--color-by2);
    color: var(--color-by8);
    font-weight: var(--font-weight-semibold);
  }
}

// Pagination customizations
.el-pagination {
  .el-pager li {
    border-radius: var(--radius-md);
  }

  .btn-prev,
  .btn-next {
    border-radius: var(--radius-md);
  }
}
