// =================================================================
// Galaxy Design System v4.0 - Element Plus Theme Override
// =================================================================

// https://github.com/element-plus/element-plus/blob/dev/packages/theme-chalk/src/common/var.scss

// Import our design tokens first
@use '../abstracts/colors';
@use '../base/typography';
@use '../base/spacing';

// Element Plus CSS Variables Override
:root {
  // ===== PRIMARY COLORS =====
  --el-color-primary: var(--color-brand-primary);
  --el-color-primary-light-3: var(--color-brand-hover);
  --el-color-primary-light-5: var(--color-brand-tag-bg);
  --el-color-primary-light-7: var(--color-brand-table-header);
  --el-color-primary-light-8: var(--color-brand-table-header);
  --el-color-primary-light-9: #ffffff;
  --el-color-primary-dark-2: var(--color-brand-active);

  // ===== SUCCESS COLORS =====
  --el-color-success: var(--color-functional-success);
  --el-color-success-light-3: var(--color-functional-success);
  --el-color-success-light-5: var(--color-functional-success-bg);
  --el-color-success-light-7: var(--color-functional-success-bg);
  --el-color-success-light-8: var(--color-functional-success-bg);
  --el-color-success-light-9: #ffffff;
  --el-color-success-dark-2: var(--color-functional-success);

  // ===== WARNING COLORS =====
  --el-color-warning: var(--color-functional-warning);
  --el-color-warning-light-3: var(--color-functional-warning);
  --el-color-warning-light-5: var(--color-functional-warning-bg);
  --el-color-warning-light-7: var(--color-functional-warning-bg);
  --el-color-warning-light-8: var(--color-functional-warning-bg);
  --el-color-warning-light-9: #ffffff;
  --el-color-warning-dark-2: var(--color-functional-warning);

  // ===== DANGER COLORS =====
  --el-color-danger: var(--color-functional-danger);
  --el-color-danger-light-3: var(--color-functional-danger);
  --el-color-danger-light-5: var(--color-functional-danger-bg);
  --el-color-danger-light-7: var(--color-functional-danger-bg);
  --el-color-danger-light-8: var(--color-functional-danger-bg);
  --el-color-danger-light-9: #ffffff;
  --el-color-danger-dark-2: var(--color-functional-danger);

  // ===== INFO COLORS =====
  --el-color-info: var(--color-text-tertiary);
  --el-color-info-light-3: var(--color-text-tertiary);
  --el-color-info-light-5: var(--color-fill-tag);
  --el-color-info-light-7: var(--color-fill-tag);
  --el-color-info-light-8: var(--color-fill-tag);
  --el-color-info-light-9: #ffffff;
  --el-color-info-dark-2: var(--color-text-secondary);

  // ===== TEXT COLORS =====
  --el-text-color-primary: var(--color-text-primary);
  --el-text-color-regular: var(--color-text-secondary);
  --el-text-color-secondary: var(--color-text-tertiary);
  --el-text-color-placeholder: var(--color-text-placeholder);
  --el-text-color-disabled: var(--color-text-placeholder);

  // ===== BORDER COLORS =====
  --el-border-color: var(--color-border-base);
  --el-border-color-light: var(--color-border-base);
  --el-border-color-lighter: var(--color-border-base);
  --el-border-color-extra-light: var(--color-fill-tag);
  --el-border-color-dark: var(--color-text-tertiary);
  --el-border-color-darker: var(--color-text-secondary);

  // ===== FILL COLORS =====
  --el-fill-color: var(--color-fill-disabled);
  --el-fill-color-light: var(--color-fill-disabled);
  --el-fill-color-lighter: #ffffff;
  --el-fill-color-extra-light: #ffffff;
  --el-fill-color-dark: var(--color-fill-tag);
  --el-fill-color-darker: var(--color-border-base);
  --el-fill-color-blank: #ffffff;

  // ===== BACKGROUND COLORS =====
  --el-bg-color: #ffffff;
  --el-bg-color-page: var(--color-fill-page-bg);
  --el-bg-color-overlay: #ffffff;

  // ===== TYPOGRAPHY =====
  --el-font-family: var(--font-family-chinese);
  --el-font-size-extra-large: var(--font-size-h2); // 18px
  --el-font-size-large: var(--font-size-body); // 16px
  --el-font-size-medium: var(--font-size-content); // 14px
  --el-font-size-base: var(--font-size-content); // 14px
  --el-font-size-small: var(--font-size-small); // 12px
  --el-font-size-extra-small: var(--font-size-small); // 12px

  // ===== SPACING =====
  --el-border-radius-base: var(--radius-lg);
  --el-border-radius-small: var(--radius-md);
  --el-border-radius-round: var(--radius-full);
  --el-border-radius-circle: var(--radius-full);

  // ===== SHADOWS =====
  --el-box-shadow: var(--shadow-base);
  --el-box-shadow-light: var(--shadow-sm);
  --el-box-shadow-dark: var(--shadow-lg);

  // ===== TRANSITIONS =====
  --el-transition-duration: var(--transition-base);
  --el-transition-duration-fast: var(--transition-fast);

  // ===== COMPONENT SPECIFIC =====
  --el-component-size-large: 40px;
  --el-component-size: 32px;
  --el-component-size-small: 24px;
}

// Dark mode overrides for Element Plus
@media (prefers-color-scheme: dark) {
  :root {
    --el-text-color-primary: var(--color-text-primary);
    --el-text-color-regular: var(--color-text-secondary);
    --el-text-color-secondary: var(--color-text-tertiary);
    --el-text-color-placeholder: var(--color-text-placeholder);
    --el-text-color-disabled: var(--color-text-placeholder);

    --el-border-color: var(--color-border-base);
    --el-border-color-light: var(--color-border-base);
    --el-border-color-lighter: var(--color-border-base);
    --el-border-color-extra-light: var(--color-fill-tag);

    --el-fill-color: var(--color-fill-disabled);
    --el-fill-color-light: var(--color-fill-disabled);
    --el-fill-color-lighter: var(--color-fill-table-bg);
    --el-fill-color-extra-light: var(--color-fill-table-bg);

    --el-bg-color: var(--color-fill-table-bg);
    --el-bg-color-page: var(--color-fill-page-bg);
    --el-bg-color-overlay: var(--color-fill-table-bg);
  }
}

// ===== COMPONENT CUSTOMIZATIONS =====

// Button customizations
.el-button {
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);

  &--large {
    padding: var(--space-4) var(--space-6);
    font-size: var(--font-size-lg);
  }

  &--default {
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-base);
  }

  &--small {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-sm);
    border-radius: var(--radius-md);
  }
}

// Card customizations
.el-card {
  border-radius: var(--radius-xl);
  border: 1px solid var(--color-by3);
  box-shadow: var(--shadow-sm);

  .el-card__header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--color-by3);
  }

  .el-card__body {
    padding: var(--space-6);
  }
}

// Tag customizations
.el-tag {
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-semibold);

  &--large {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-sm);
  }

  &--default {
    padding: var(--space-1) var(--space-3);
    font-size: var(--font-size-xs);
  }

  &--small {
    padding: var(--space-1) var(--space-2);
    font-size: 0.6875rem;
    border-radius: var(--radius-base);
  }
}

// Input customizations
.el-input {
  .el-input__inner {
    border-radius: var(--radius-lg);
    font-family: var(--font-family-chinese);
  }
}

// Select customizations
.el-select {
  .el-input__inner {
    border-radius: var(--radius-lg);
  }
}

// Dialog customizations
.el-dialog {
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
}

// Message customizations
.el-message {
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
}

// Notification customizations
.el-notification {
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
}

// Table customizations
.el-table {
  border-radius: var(--radius-lg);
  background-color: var(--color-fill-table-bg);

  th {
    background-color: var(--color-fill-table-header);
    color: var(--color-text-primary);
    font-weight: var(--font-weight-semibold);
  }

  td {
    background-color: var(--color-fill-table-bg);
  }

  .el-table__row:hover td {
    background-color: var(--color-brand-table-header);
  }
}

// Pagination customizations
.el-pagination {
  .el-pager li {
    border-radius: var(--radius-md);
  }

  .btn-prev,
  .btn-next {
    border-radius: var(--radius-md);
  }
}
