// SCSS utility functions

// Convert px to rem
@function rem($pixels, $base: 16) {
  @return #{$pixels / $base}rem;
}

// Z-index management
$z-indexes: (
  'dropdown': 100,
  'modal': 200,
  'toast': 300,
  'tooltip': 400
);

@function z($level) {
  @return map-get($z-indexes, $level);
}

// Color opacity
@function alpha($color, $opacity) {
  @return rgba($color, $opacity);
}

// Strip units from values
@function strip-unit($value) {
  @return $value / ($value * 0 + 1);
}
