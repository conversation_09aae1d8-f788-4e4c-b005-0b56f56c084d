// =================================================================
// Galaxy Design System v4.0 - New Color System
// =================================================================
@use 'sass:map';

:root {
  // ===== 品牌色 =====
  --color-brand-primary: #0f65dd; // 常规色
  --color-brand-hover: #4a8ce8; // 悬浮
  --color-brand-active: #1759b5; // 点击
  --color-brand-tag-bg: #e6edfc; // 标签背景色
  --color-brand-table-header: #edf3fd; // 表头背景色

  // ===== 功能色 =====
  --color-functional-primary: #0f65dd; // 通过文字/链接文字/常规操作按钮文字
  --color-functional-primary-bg: #e6edfc; // 标签背景
  --color-functional-danger: #c20000; // 警示/驳回文字/涨停
  --color-functional-danger-bg: #ffebeb; // 标签背景
  --color-functional-warning: #e98900; // 预警文字
  --color-functional-warning-bg: #ffeed6; // 标签背景
  --color-functional-success: #148a0c; // 进行中/成功/跌停文字
  --color-functional-success-bg: #edfdec; // 标签背景

  // ===== 字体颜色 =====
  --color-text-primary: #20212b; // 主要文字(一级内容/页面标题)
  --color-text-secondary: #5f6978; // 次要文字
  --color-text-tertiary: #8f98a8; // 辅助说明文字/图标
  --color-text-placeholder: #c5cbd6; // 预设文字
  --color-text-white: #ffffff; // Button/操作文字
  --color-text-link: #0f65dd; // 通过文字/链接文字/常规操作按钮文字
  --color-text-danger: #c20000; // 警示/驳回文字/涨停
  --color-text-warning: #e98900; // 预警文字
  --color-text-success: #148a0c; // 进行中/成功/跌停文字

  // ===== 基础颜色 - 描边色 =====
  --color-border-base: #e8eaed; // 常规描边(下拉选择/表格)

  // ===== 基础颜色 - 填充色 =====
  --color-fill-emphasis: #5f6978; // 强调/图标/特殊场景/次要文字
  --color-fill-scrollbar: #d1d5db; // 滚动条/步骤条
  --color-fill-table-header: #edf3fd; // 表头背景色
  --color-fill-tag: #e8eaed; // 标签背景
  --color-fill-disabled: #f0f0f2; // 禁用(下拉选择/表单填写)
  --color-fill-page-bg: #f3f6fa; // 页面背景色
  --color-fill-table-bg: #f9fafe; // 表格背景色

  // ===== 禁用状态 (30%不透明度) =====
  --color-text-primary-disabled: rgba(32, 33, 43, 0.3); // 主要文字禁用
  --color-text-secondary-disabled: rgba(95, 105, 120, 0.3); // 次要文字禁用
  --color-text-tertiary-disabled: rgba(143, 152, 168, 0.3); // 辅助文字禁用
  --color-text-link-disabled: rgba(15, 101, 221, 0.3); // 链接文字禁用
  --color-text-danger-disabled: rgba(194, 0, 0, 0.3); // 危险文字禁用
  --color-text-warning-disabled: rgba(233, 137, 0, 0.3); // 警告文字禁用
  --color-text-success-disabled: rgba(20, 138, 12, 0.3); // 成功文字禁用

  // ===== 语义化颜色 =====
  --color-semantic-primary: var(--color-functional-primary);
  --color-semantic-success: var(--color-functional-success);
  --color-semantic-warning: var(--color-functional-warning);
  --color-semantic-danger: var(--color-functional-danger);
  --color-semantic-info: var(--color-functional-primary);
}

// ===== DARK MODE OVERRIDES =====
@media (prefers-color-scheme: dark) {
  :root {
    // 暗色模式下的文字颜色调整
    --color-text-primary: #ffffff;
    --color-text-secondary: #c5cbd6;
    --color-text-tertiary: #8f98a8;
    --color-text-placeholder: #5f6978;

    // 暗色模式下的填充色调整
    --color-fill-page-bg: #1a1b23;
    --color-fill-table-bg: #20212b;
    --color-fill-table-header: #2a2d3a;
    --color-fill-disabled: #2a2d3a;
    --color-border-base: #3a3d4a;
  }
}

// ===== SCSS VARIABLES FOR COMPONENT USE =====

// 品牌色
$color-brand-primary: #0f65dd;
$color-brand-hover: #4a8ce8;
$color-brand-active: #1759b5;
$color-brand-tag-bg: #e6edfc;
$color-brand-table-header: #edf3fd;

// 功能色
$color-functional-primary: #0f65dd;
$color-functional-primary-bg: #e6edfc;
$color-functional-danger: #c20000;
$color-functional-danger-bg: #ffebeb;
$color-functional-warning: #e98900;
$color-functional-warning-bg: #ffeed6;
$color-functional-success: #148a0c;
$color-functional-success-bg: #edfdec;

// 字体颜色
$color-text-primary: #20212b;
$color-text-secondary: #5f6978;
$color-text-tertiary: #8f98a8;
$color-text-placeholder: #c5cbd6;
$color-text-white: #ffffff;
$color-text-link: #0f65dd;
$color-text-danger: #c20000;
$color-text-warning: #e98900;
$color-text-success: #148a0c;

// 基础颜色
$color-border-base: #e8eaed;
$color-fill-emphasis: #5f6978;
$color-fill-scrollbar: #d1d5db;
$color-fill-table-header: #edf3fd;
$color-fill-tag: #e8eaed;
$color-fill-disabled: #f0f0f2;
$color-fill-page-bg: #f3f6fa;
$color-fill-table-bg: #f9fafe;

// 语义化颜色
$color-primary: $color-functional-primary;
$color-success: $color-functional-success;
$color-warning: $color-functional-warning;
$color-danger: $color-functional-danger;
$color-info: $color-functional-primary;

// 兼容性别名
$text-color-primary: $color-text-primary;
$text-color-regular: $color-text-secondary;
$text-color-secondary: $color-text-tertiary;
$text-color-placeholder: $color-text-placeholder;
$border-color-base: $color-border-base;
$bg-color-page: $color-fill-page-bg;

// SCSS Color Maps
$brand-colors: (
  'primary': $color-brand-primary,
  'hover': $color-brand-hover,
  'active': $color-brand-active,
  'tag-bg': $color-brand-tag-bg,
  'table-header': $color-brand-table-header,
);

$functional-colors: (
  'primary': $color-functional-primary,
  'primary-bg': $color-functional-primary-bg,
  'danger': $color-functional-danger,
  'danger-bg': $color-functional-danger-bg,
  'warning': $color-functional-warning,
  'warning-bg': $color-functional-warning-bg,
  'success': $color-functional-success,
  'success-bg': $color-functional-success-bg,
);

$text-colors: (
  'primary': $color-text-primary,
  'secondary': $color-text-secondary,
  'tertiary': $color-text-tertiary,
  'placeholder': $color-text-placeholder,
  'white': $color-text-white,
  'link': $color-text-link,
  'danger': $color-text-danger,
  'warning': $color-text-warning,
  'success': $color-text-success,
);

$fill-colors: (
  'emphasis': $color-fill-emphasis,
  'scrollbar': $color-fill-scrollbar,
  'table-header': $color-fill-table-header,
  'tag': $color-fill-tag,
  'disabled': $color-fill-disabled,
  'page-bg': $color-fill-page-bg,
  'table-bg': $color-fill-table-bg,
);

$colors: map.merge(
  map.merge($brand-colors, $functional-colors),
  map.merge($text-colors, $fill-colors)
);
