// Mixins for reusable style patterns

// Responsive breakpoints
@mixin respond-to($breakpoint) {
  @if $breakpoint == mobile {
    @media (max-width: 767px) { @content; }
  }
  @if $breakpoint == tablet {
    @media (min-width: 768px) and (max-width: 1023px) { @content; }
  }
  @if $breakpoint == desktop {
    @media (min-width: 1024px) { @content; }
  }
}

// Flexbox utilities
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// Button styles
@mixin btn-reset {
  border: none;
  background: none;
  padding: 0;
  cursor: pointer;
  outline: none;
}

// Text truncation
@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Card shadow
@mixin card-shadow {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// Hover transition
@mixin hover-transition($property: all, $duration: 0.3s) {
  transition: $property $duration ease;
}
