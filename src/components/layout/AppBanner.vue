<template>
  <section class="app-banner">
    <div class="banner-content">
      <h1 class="banner-title">Vue 3 + Element Plus</h1>
      <p class="banner-subtitle">现代化的开发者友好前端框架</p>
      <div class="banner-description">
        <p>基于 Vue 3、TypeScript 和 Element Plus 构建的企业级前端框架，提供丰富的组件和功能，助力快速开发高质量应用。</p>
      </div>
      <div class="banner-actions">
        <router-link to="/components" class="btn btn-primary">浏览组件</router-link>
        <router-link to="/docs" class="btn btn-secondary">阅读文档</router-link>
        <a href="https://github.com/your-repo/vue3-element-plus" target="_blank" class="btn btn-outline">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor" class="btn-icon">
            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
          </svg>
          GitHub
        </a>
      </div>
      <div class="banner-stats">
        <div class="stat-item">
          <span class="stat-number">3.2.13</span>
          <span class="stat-label">Vue 版本</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">50+</span>
          <span class="stat-label">组件</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">100%</span>
          <span class="stat-label">TypeScript</span>
        </div>
      </div>
    </div>
    <div class="banner-decoration">
      <div class="code-block">
        <pre><code>
import { createApp } from 'vue'
import App from './App.vue'
import ElementPlus from 'element-plus'
          
// 创建 Vue 应用
const app = createApp(App)
          
// 使用插件
app.use(ElementPlus)
          
// 挂载应用
app.mount('#app')
        </code></pre>
      </div>
    </div>
  </section>
</template>

<script setup>
// Banner component for the homepage
</script>

<style lang="scss" scoped>
.app-banner {
  width: 100%;
  background: linear-gradient(135deg, var(--color-background), var(--color-background-soft));
  padding: 4rem 2rem;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  
  &::before {
    content: "";
    position: absolute;
    top: -200px;
    right: -200px;
    width: 600px;
    height: 600px;
    border-radius: 50%;
    background: radial-gradient(
      rgba(var(--color-primary-rgb), 0.1),
      rgba(var(--color-primary-rgb), 0)
    );
    z-index: 0;
  }
  
  &::after {
    content: "";
    position: absolute;
    bottom: -150px;
    left: -150px;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: radial-gradient(
      rgba(var(--color-primary-rgb), 0.08),
      rgba(var(--color-primary-rgb), 0)
    );
    z-index: 0;
  }
  
  .banner-content {
    max-width: 1200px;
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
  }
  
  .banner-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: linear-gradient(120deg, var(--color-primary), #6574cd);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 1.1;
  }
  
  .banner-subtitle {
    font-size: 1.5rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
    color: var(--color-text);
  }
  
  .banner-description {
    margin-bottom: 2rem;
    
    p {
      font-size: 1.125rem;
      line-height: 1.6;
      color: var(--color-text-light);
      margin-bottom: 1rem;
    }
  }
  
  .banner-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2.5rem;
    
    .btn {
      display: inline-flex;
      align-items: center;
      padding: 0.75rem 1.5rem;
      border-radius: 0.375rem;
      font-weight: 600;
      font-size: 1rem;
      transition: all 0.2s;
      text-decoration: none;
      
      .btn-icon {
        margin-right: 0.5rem;
      }
      
      &-primary {
        background-color: var(--color-primary);
        color: white;
        
        &:hover {
          background-color: var(--color-primary-dark);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.25);
        }
      }
      
      &-secondary {
        background-color: var(--color-background-mute);
        color: var(--color-heading);
        
        &:hover {
          background-color: var(--color-background-soft);
          transform: translateY(-2px);
        }
      }
      
      &-outline {
        background-color: transparent;
        border: 1px solid var(--color-border);
        color: var(--color-text);
        
        &:hover {
          border-color: var(--color-primary);
          color: var(--color-primary);
          transform: translateY(-2px);
        }
      }
    }
  }
  
  .banner-stats {
    display: flex;
    gap: 2rem;
    
    .stat-item {
      display: flex;
      flex-direction: column;
      
      .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--color-heading);
      }
      
      .stat-label {
        font-size: 0.875rem;
        color: var(--color-text-light);
      }
    }
  }
  
  .banner-decoration {
    .code-block {
      background-color: var(--color-background-soft);
      padding: 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      position: relative;
      overflow: hidden;
      
      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 30px;
        background-color: var(--color-background-mute);
        border-bottom: 1px solid var(--color-border);
        display: flex;
        align-items: center;
        padding: 0 1rem;
      }
      
      pre {
        margin-top: 20px;
        font-family: 'Fira Code', 'Menlo', monospace;
        
        code {
          color: var(--color-text-light);
          line-height: 1.7;
        }
      }
    }
  }
}

@media (max-width: 992px) {
  .app-banner {
    .banner-content {
      grid-template-columns: 1fr;
      gap: 3rem;
    }
    
    .banner-title {
      font-size: 2.5rem;
    }
    
    .banner-actions {
      flex-wrap: wrap;
    }
  }
}

@media (max-width: 576px) {
  .app-banner {
    padding: 3rem 1rem;
    
    .banner-title {
      font-size: 2rem;
    }
    
    .banner-subtitle {
      font-size: 1.25rem;
    }
    
    .banner-stats {
      flex-direction: column;
      gap: 1rem;
    }
  }
}
</style>