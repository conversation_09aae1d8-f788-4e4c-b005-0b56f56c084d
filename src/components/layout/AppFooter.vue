<template>
  <footer class="app-footer">
    <div class="footer-container">
      <div class="footer-sections">
        <div class="footer-section">
          <h3 class="section-title">Vue3 Element Plus</h3>
          <p class="section-description">
            现代化的开发者友好前端框架，基于 Vue 3、TypeScript 和 Element Plus，
            提供企业级组件和功能，助力快速开发高质量应用。
          </p>
          <div class="social-links">
            <a href="https://github.com/your-repo/vue3-element-plus" target="_blank" class="social-link">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
            </a>
          </div>
        </div>
        <div class="footer-section">
          <h3 class="section-title">快速链接</h3>
          <ul class="footer-links">
            <li><router-link to="/" class="footer-link">首页</router-link></li>
            <li><router-link to="/dashboard" class="footer-link">数据看板</router-link></li>
            <li><router-link to="/components/data-grid" class="footer-link">组件</router-link></li>
            <li><router-link to="/about" class="footer-link">关于</router-link></li>
            <li><router-link to="/design-system" class="footer-link">设计系统</router-link></li>
          </ul>
        </div>
        <div class="footer-section">
          <h3 class="section-title">资源</h3>
          <ul class="footer-links">
            <li><a href="https://v3.vuejs.org/" target="_blank" class="footer-link">Vue 3 文档</a></li>
            <li><a href="https://element-plus.org/" target="_blank" class="footer-link">Element Plus</a></li>
            <li><a href="https://www.typescriptlang.org/" target="_blank" class="footer-link">TypeScript</a></li>
            <li><a href="https://github.com/your-repo/vue3-element-plus" target="_blank" class="footer-link">GitHub 仓库</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h3 class="section-title">订阅更新</h3>
          <p class="section-description">订阅我们的通讯，获取最新的框架更新和教程。</p>
        </div>
      </div>
      <div class="footer-bottom">
        <p class="copyright">&copy; {{ currentYear }} Vue3 Element Plus. 保留所有权利。</p>
        <div class="footer-bottom-links">
          <router-link to="/privacy" class="bottom-link">隐私政策</router-link>
          <router-link to="/terms" class="bottom-link">使用条款</router-link>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const currentYear = ref(new Date().getFullYear())
</script>

<style lang="scss" scoped>
.app-footer {
  background-color: var(--color-background-soft);
  border-top: 1px solid var(--color-border);
  padding: 4rem 2rem 2rem;
  color: var(--color-text);

  .footer-container {
    max-width: 1280px;
    margin: 0 auto;
  }

  .footer-sections {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
  }

  .footer-section {
    .section-title {
      font-size: 1.125rem;
      font-weight: 600;
      margin-bottom: 1.25rem;
      color: var(--color-heading);
    }

    .section-description {
      font-size: 0.875rem;
      line-height: 1.6;
      margin-bottom: 1.25rem;
      color: var(--color-text-light);
    }

    .social-links {
      display: flex;
      gap: 1rem;

      .social-link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        background-color: var(--color-background-mute);
        border-radius: 0.375rem;
        color: var(--color-text);
        transition: all 0.2s;

        &:hover {
          background-color: var(--color-primary);
          color: white;
          transform: translateY(-3px);
        }
      }
    }

    .footer-links {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        margin-bottom: 0.75rem;
      }

      .footer-link {
        color: var(--color-text-light);
        text-decoration: none;
        font-size: 0.875rem;
        transition: color 0.2s;

        &:hover {
          color: var(--color-primary);
        }
      }
    }

    .subscribe-form {
      display: flex;
      margin-top: 1rem;

      .subscribe-input {
        flex: 1;
        padding: 0.625rem 1rem;
        background-color: var(--color-background);
        border: 1px solid var(--color-border);
        border-radius: 0.375rem 0 0 0.375rem;
        font-size: 0.875rem;
        color: var(--color-text);

        &:focus {
          outline: none;
          border-color: var(--color-primary);
        }
      }

      .subscribe-button {
        padding: 0.625rem 1rem;
        background-color: var(--color-primary);
        color: white;
        border: none;
        border-radius: 0 0.375rem 0.375rem 0;
        font-size: 0.875rem;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background-color: var(--color-primary-dark);
        }
      }
    }
  }

  .footer-bottom {
    padding-top: 2rem;
    border-top: 1px solid var(--color-border);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .copyright {
      font-size: 0.875rem;
      color: var(--color-text-light);
    }

    .footer-bottom-links {
      display: flex;
      gap: 1.5rem;

      .bottom-link {
        font-size: 0.875rem;
        color: var(--color-text-light);
        text-decoration: none;

        &:hover {
          color: var(--color-primary);
        }
      }
    }
  }
}

@media (max-width: 992px) {
  .app-footer {
    .footer-sections {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

@media (max-width: 576px) {
  .app-footer {
    padding: 3rem 1rem 1.5rem;

    .footer-sections {
      grid-template-columns: 1fr;
      gap: 2rem;
    }

    .footer-bottom {
      flex-direction: column;
      gap: 1rem;
      text-align: center;

      .copyright {
        margin-bottom: 0.5rem;
      }
    }
  }
}
</style>
