<template>
  <header class="app-navbar">
    <div class="navbar-container">
      <div class="navbar-logo">
        <router-link to="/" class="logo-link">
          <span class="logo-text">{{ appName }}</span>
        </router-link>
      </div>
      <nav class="navbar-links">
        <div class="nav-group">
          <router-link to="/" class="nav-item" exact-active-class="router-link-active">首页
          </router-link>
          <router-link to="/dashboard" class="nav-item" active-class="router-link-active">数据看板
          </router-link>

          <!-- 自定义下拉菜单实现 -->
          <div
            class="nav-dropdown"
            :class="{ 'is-active': isComponentsActive }"
            @mouseenter="showDropdown = true"
            @mouseleave="showDropdown = false"
          >
            <span class="nav-item dropdown-toggle"
                  :class="{ 'router-link-active': isComponentsActive }">
              组件
              <svg
                class="dropdown-icon"
                :class="{ 'rotate': showDropdown }"
                viewBox="0 0 1024 1024"
              >
                <path fill="currentColor" d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"></path>
              </svg>
            </span>
            <div class="dropdown-menu-container" v-show="showDropdown">
              <div class="dropdown-menu">
                <router-link
                  to="/components/data-grid"
                  class="dropdown-item"
                  active-class="router-link-active"
                >
                  DataGrid
                </router-link>
                <!-- 未来可添加更多组件 -->
              </div>
            </div>
          </div>

          <router-link to="/design-system" class="nav-item" active-class="router-link-active">
            设计系统
          </router-link>
          <router-link to="/about" class="nav-item" active-class="router-link-active">关于
          </router-link>
        </div>
      </nav>
    </div>
  </header>
</template>

<script setup>
import { APP_NAME } from '@/constants'
import { computed, ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const appName = APP_NAME
const route = useRoute()
const showDropdown = ref(false)

// 计算当前是否在组件路由下
const isComponentsActive = computed(() => {
  return route.path.startsWith('/components')
})

// 监听路由变化，可以在这里添加其他逻辑
watch(() => route.path, (newPath) => {
  // 路由变化时关闭下拉菜单
  showDropdown.value = false
})
</script>

<style lang="scss" scoped>
@import '@/styles/abstracts/_colors.scss';
@import '@/styles/abstracts/_mixins.scss';

.app-navbar {
  background-color: $color-by1; /* 替换 var(--color-background) */
  border-bottom: 1px solid $color-by4; /* 替换 var(--color-border) */
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .navbar-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0.75rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .navbar-logo {
    .logo-link {
      display: flex;
      align-items: center;
      text-decoration: none;

      .logo-image {
        height: 32px;
        margin-right: 0.75rem;
      }

      .logo-text {
        font-size: 1.25rem;
        font-weight: 700;
        color: $color-by8; /* 替换 var(--color-heading) */
        background: linear-gradient(120deg, #3490dc, #6574cd);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        letter-spacing: -0.5px;
      }
    }
  }

  .navbar-links {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    padding-left: 2rem;

    .nav-group {
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }

    .nav-item {
      color: $color-by7;
      text-decoration: none;
      padding: 0.5rem 0.75rem;
      border-radius: 0.25rem;
      font-weight: 500;
      transition: all 0.2s ease;
      position: relative;

      &:hover {
        color: $color-by8;
        background-color: $color-by2;
      }

      &.router-link-active {
        color: $color-blue-main;

        &::after {
          content: '';
          position: absolute;
          bottom: -3px;
          left: 0.5rem;
          right: 0.5rem;
          height: 2px;
          background-color: $color-blue-main;
          border-radius: 2px;
        }
      }
    }

    .nav-dropdown {
      position: relative;

      .dropdown-toggle {
        display: flex;
        align-items: center;
        cursor: pointer;

        &.router-link-active {
          color: $color-blue-main;

          &::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0.5rem;
            right: 0.5rem;
            height: 2px;
            background-color: $color-blue-main;
            border-radius: 2px;
          }
        }
      }

      .dropdown-icon {
        width: 14px;
        height: 14px;
        margin-left: 4px;
        transition: transform 0.2s;

        &.rotate {
          transform: rotate(180deg);
        }
      }

      .dropdown-menu-container {
        position: absolute;
        top: 100%;
        left: 0;
        padding-top: 0.5rem;
        z-index: 10;
      }

      .dropdown-menu {
        background-color: $color-by1;
        border: 1px solid $color-by4;
        border-radius: 0.375rem;
        padding: 0.5rem 0;
        min-width: 120px;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      }

      .dropdown-item {
        display: block;
        padding: 0.5rem 1rem;
        color: $color-by7;
        text-decoration: none;
        font-size: 0.875rem;
        transition: all 0.2s;
        text-align: left;

        &:hover {
          background-color: $color-by2;
          color: $color-blue-main;
        }

        &.router-link-active {
          color: $color-blue-main;
          background-color: rgba($color-blue-main, 0.1);
        }
      }
    }

    .nav-dropdown.is-active .dropdown-toggle {
      color: $color-blue-main;
    }

    .nav-actions {
      display: flex;
      gap: 0.5rem;

      .action-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 0.375rem;
        color: $color-by7;
        background: transparent;
        border: 1px solid $color-by4;
        cursor: pointer;
        transition: all 0.2s;

        svg {
          width: 20px;
          height: 20px;
        }

        &:hover {
          background-color: $color-by2; /* 替换 var(--color-background-mute) */
          color: $color-by8; /* 替换 var(--color-heading) */
          border-color: $color-by3; /* 替换 var(--color-border-hover) */
        }

        &.github:hover {
          color: #333;
          background-color: #f6f8fa;
        }
      }
    }
  }
}

@include respond-to('tablet') {
  .app-navbar {
    .navbar-links {
      .nav-group {
        display: none;
      }
    }
  }
}
</style>
