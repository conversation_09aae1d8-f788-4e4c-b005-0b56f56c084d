<template>
  <div class="json-schema-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="mergedRules"
      :label-width="labelWidth"
      :label-position="labelPosition"
      :inline="config.layout === 'inline'"
      :disabled="config.disabled"
      :validate-on-rule-change="false"
    >
      <template v-for="(field, key) in sortedFields" :key="key">
        <el-form-item
          v-if="isFieldVisible(key, field)"
          :label="field.title || key"
          :prop="key"
          :required="isFieldRequired(key)"
        >
          <!-- 输入框 -->
          <el-input
            v-if="getFieldWidget(key, field) === 'input'"
            v-model="formData[key]"
            :placeholder="getFieldPlaceholder(key, field)"
            :disabled="isFieldDisabled(key, field)"
            :readonly="isFieldReadonly(key, field)"
            :maxlength="field.maxLength"
            :show-word-limit="!!field.maxLength"
            clearable
          />

          <!-- 文本域 -->
          <el-input
            v-else-if="getFieldWidget(key, field) === 'textarea'"
            v-model="formData[key]"
            type="textarea"
            :placeholder="getFieldPlaceholder(key, field)"
            :disabled="isFieldDisabled(key, field)"
            :readonly="isFieldReadonly(key, field)"
            :maxlength="field.maxLength"
            :show-word-limit="!!field.maxLength"
            :rows="getFieldOption(key, 'rows', 3)"
          />

          <!-- 数字输入框 -->
          <el-input-number
            v-else-if="getFieldWidget(key, field) === 'number'"
            v-model="formData[key]"
            :placeholder="getFieldPlaceholder(key, field)"
            :disabled="isFieldDisabled(key, field)"
            :readonly="isFieldReadonly(key, field)"
            :min="field.minimum"
            :max="field.maximum"
            :precision="getFieldOption(key, 'precision')"
            :step="getFieldOption(key, 'step', 1)"
            style="width: 100%"
          />

          <!-- 选择器 -->
          <el-select
            v-else-if="getFieldWidget(key, field) === 'select'"
            v-model="formData[key]"
            :placeholder="getFieldPlaceholder(key, field)"
            :disabled="isFieldDisabled(key, field)"
            :multiple="getFieldOption(key, 'multiple', false)"
            :filterable="getFieldOption(key, 'filterable', false)"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="option in getFieldOptions(key, field)"
              :key="option.value"
              :label="option.label"
              :value="option.value"
              :disabled="option.disabled"
            />
          </el-select>

          <!-- 单选框组 -->
          <el-radio-group
            v-else-if="getFieldWidget(key, field) === 'radio'"
            v-model="formData[key]"
            :disabled="isFieldDisabled(key, field)"
          >
            <el-radio
              v-for="option in getFieldOptions(key, field)"
              :key="option.value"
              :label="option.value"
              :disabled="option.disabled"
            >
              {{ option.label }}
            </el-radio>
          </el-radio-group>

          <!-- 复选框组 -->
          <el-checkbox-group
            v-else-if="getFieldWidget(key, field) === 'checkbox'"
            v-model="formData[key]"
            :disabled="isFieldDisabled(key, field)"
          >
            <el-checkbox
              v-for="option in getFieldOptions(key, field)"
              :key="option.value"
              :label="option.value"
              :disabled="option.disabled"
            >
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>

          <!-- 开关 -->
          <el-switch
            v-else-if="getFieldWidget(key, field) === 'switch'"
            v-model="formData[key]"
            :disabled="isFieldDisabled(key, field)"
            :active-text="getFieldOption(key, 'activeText')"
            :inactive-text="getFieldOption(key, 'inactiveText')"
          />

          <!-- 日期选择器 -->
          <el-date-picker
            v-else-if="getFieldWidget(key, field) === 'date'"
            v-model="formData[key]"
            type="date"
            :placeholder="getFieldPlaceholder(key, field)"
            :disabled="isFieldDisabled(key, field)"
            :readonly="isFieldReadonly(key, field)"
            :format="getFieldOption(key, 'format', 'YYYY-MM-DD')"
            :value-format="getFieldOption(key, 'valueFormat', 'YYYY-MM-DD')"
            style="width: 100%"
          />

          <!-- 时间选择器 -->
          <el-time-picker
            v-else-if="getFieldWidget(key, field) === 'time'"
            v-model="formData[key]"
            :placeholder="getFieldPlaceholder(key, field)"
            :disabled="isFieldDisabled(key, field)"
            :readonly="isFieldReadonly(key, field)"
            :format="getFieldOption(key, 'format', 'HH:mm:ss')"
            :value-format="getFieldOption(key, 'valueFormat', 'HH:mm:ss')"
            style="width: 100%"
          />

          <!-- 日期时间选择器 -->
          <el-date-picker
            v-else-if="getFieldWidget(key, field) === 'datetime'"
            v-model="formData[key]"
            type="datetime"
            :placeholder="getFieldPlaceholder(key, field)"
            :disabled="isFieldDisabled(key, field)"
            :readonly="isFieldReadonly(key, field)"
            :format="getFieldOption(key, 'format', 'YYYY-MM-DD HH:mm:ss')"
            :value-format="getFieldOption(key, 'valueFormat', 'YYYY-MM-DD HH:mm:ss')"
            style="width: 100%"
          />

          <!-- 颜色选择器 -->
          <el-color-picker
            v-else-if="getFieldWidget(key, field) === 'color'"
            v-model="formData[key]"
            :disabled="isFieldDisabled(key, field)"
            :show-alpha="getFieldOption(key, 'showAlpha', false)"
          />

          <!-- 滑块 -->
          <el-slider
            v-else-if="getFieldWidget(key, field) === 'slider'"
            v-model="formData[key]"
            :disabled="isFieldDisabled(key, field)"
            :min="field.minimum || 0"
            :max="field.maximum || 100"
            :step="getFieldOption(key, 'step', 1)"
            :show-input="getFieldOption(key, 'showInput', false)"
          />

          <!-- 评分 -->
          <el-rate
            v-else-if="getFieldWidget(key, field) === 'rate'"
            v-model="formData[key]"
            :disabled="isFieldDisabled(key, field)"
            :max="getFieldOption(key, 'max', 5)"
            :allow-half="getFieldOption(key, 'allowHalf', false)"
            :show-text="getFieldOption(key, 'showText', false)"
          />

          <!-- 上传 -->
          <el-upload
            v-else-if="getFieldWidget(key, field) === 'upload'"
            v-model:file-list="formData[key]"
            :action="getFieldOption(key, 'action', '#')"
            :disabled="isFieldDisabled(key, field)"
            :multiple="getFieldOption(key, 'multiple', false)"
            :limit="getFieldOption(key, 'limit')"
            :accept="getFieldOption(key, 'accept')"
            :auto-upload="getFieldOption(key, 'autoUpload', true)"
          >
            <el-button type="primary">
              <el-icon><Upload /></el-icon>
              {{ getFieldOption(key, 'buttonText', '选择文件') }}
            </el-button>
          </el-upload>

          <!-- 自定义组件 -->
          <component
            v-else-if="getFieldWidget(key, field) === 'custom'"
            :is="getCustomComponent(key)"
            v-model="formData[key]"
            v-bind="getFieldOption(key, 'componentProps', {})"
            :disabled="isFieldDisabled(key, field)"
            :readonly="isFieldReadonly(key, field)"
          />

          <!-- 对象类型（嵌套表单） -->
          <div v-else-if="field.type === 'object'" class="nested-form">
            <JsonSchemaForm
              v-model="formData[key]"
              :config="{
                schema: field,
                uiSchema: getNestedUiSchema(key),
                layout: config.layout,
                labelWidth: config.labelWidth,
                disabled: config.disabled || isFieldDisabled(key, field),
                readonly: config.readonly || isFieldReadonly(key, field)
              }"
            />
          </div>

          <!-- 数组类型 -->
          <div v-else-if="field.type === 'array'" class="array-field">
            <div
              v-for="(item, index) in (formData[key] || [])"
              :key="index"
              class="array-item"
            >
              <JsonSchemaForm
                v-if="field.items?.type === 'object'"
                v-model="formData[key][index]"
                :config="{
                  schema: field.items,
                  uiSchema: getNestedUiSchema(key, 'items'),
                  layout: 'inline',
                  labelWidth: config.labelWidth,
                  disabled: config.disabled || isFieldDisabled(key, field),
                  readonly: config.readonly || isFieldReadonly(key, field)
                }"
              />
              <component
                v-else
                :is="getArrayItemWidget(key, field)"
                v-model="formData[key][index]"
                :disabled="isFieldDisabled(key, field)"
                :readonly="isFieldReadonly(key, field)"
              />
              <el-button
                type="danger"
                size="small"
                text
                :disabled="isFieldDisabled(key, field)"
                @click="removeArrayItem(key, index)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
            <el-button
              type="primary"
              size="small"
              text
              :disabled="isFieldDisabled(key, field)"
              @click="addArrayItem(key, field)"
            >
              <el-icon><Plus /></el-icon>
              添加
            </el-button>
          </div>

          <!-- 帮助文本 -->
          <div v-if="getFieldHelp(key)" class="field-help">
            {{ getFieldHelp(key) }}
          </div>
        </el-form-item>
      </template>
    </el-form>

    <!-- 表单操作按钮 -->
    <div v-if="!config.readonly" class="form-actions">
      <el-button type="primary" @click="handleSubmit">
        提交
      </el-button>
      <el-button @click="handleReset">
        重置
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, inject } from 'vue'
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElSelect,
  ElOption,
  ElRadioGroup,
  ElRadio,
  ElCheckboxGroup,
  ElCheckbox,
  ElSwitch,
  ElDatePicker,
  ElTimePicker,
  ElColorPicker,
  ElSlider,
  ElRate,
  ElUpload,
  ElButton,
  ElIcon
} from 'element-plus'
import { Upload, Delete, Plus } from '@element-plus/icons-vue'
import type {
  JsonSchemaFormConfig,
  JsonSchema,
  JsonSchemaProperty,
  UiSchema,
  CustomComponent
} from '@/types/data-grid'

interface Props {
  modelValue?: any
  config: JsonSchemaFormConfig
}

interface Emits {
  (e: 'update:modelValue', value: any): void
  (e: 'submit', value: any): void
  (e: 'reset'): void
  (e: 'validate', valid: boolean, errors: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<InstanceType<typeof ElForm>>()
const formData = ref<any>({})

// 注入自定义组件
const customComponents = inject<Map<string, CustomComponent>>('customComponents', new Map())

// 计算属性
const labelWidth = computed(() => {
  return props.config.labelWidth || '120px'
})

const labelPosition = computed(() => {
  return props.config.layout === 'horizontal' ? 'right' : 'top'
})

const sortedFields = computed(() => {
  const { schema, uiSchema } = props.config
  const fields = schema.properties || {}
  
  // 根据 ui:order 排序
  const order = uiSchema?.['ui:order'] || Object.keys(fields)
  const sortedEntries: [string, JsonSchemaProperty][] = []
  
  // 按照指定顺序添加字段
  order.forEach(key => {
    if (fields[key]) {
      sortedEntries.push([key, fields[key]])
    }
  })
  
  // 添加未在顺序中指定的字段
  Object.entries(fields).forEach(([key, field]) => {
    if (!order.includes(key)) {
      sortedEntries.push([key, field])
    }
  })
  
  return Object.fromEntries(sortedEntries)
})

const mergedRules = computed(() => {
  const rules: Record<string, any[]> = {}
  const { schema, rules: configRules } = props.config
  
  Object.entries(schema.properties || {}).forEach(([key, field]) => {
    const fieldRules: any[] = []
    
    // 必填验证
    if (isFieldRequired(key)) {
      fieldRules.push({
        required: true,
        message: `请输入${field.title || key}`,
        trigger: getValidationTrigger(key, field)
      })
    }
    
    // 字符串长度验证
    if (field.type === 'string') {
      if (field.minLength !== undefined) {
        fieldRules.push({
          min: field.minLength,
          message: `${field.title || key}长度不能少于${field.minLength}个字符`,
          trigger: 'blur'
        })
      }
      if (field.maxLength !== undefined) {
        fieldRules.push({
          max: field.maxLength,
          message: `${field.title || key}长度不能超过${field.maxLength}个字符`,
          trigger: 'blur'
        })
      }
      if (field.pattern) {
        fieldRules.push({
          pattern: new RegExp(field.pattern),
          message: `${field.title || key}格式不正确`,
          trigger: 'blur'
        })
      }
    }
    
    // 数字范围验证
    if (field.type === 'number' || field.type === 'integer') {
      if (field.minimum !== undefined) {
        fieldRules.push({
          type: 'number',
          min: field.minimum,
          message: `${field.title || key}不能小于${field.minimum}`,
          trigger: 'blur'
        })
      }
      if (field.maximum !== undefined) {
        fieldRules.push({
          type: 'number',
          max: field.maximum,
          message: `${field.title || key}不能大于${field.maximum}`,
          trigger: 'blur'
        })
      }
    }
    
    // 合并自定义规则
    if (configRules?.[key]) {
      fieldRules.push(...configRules[key])
    }
    
    if (fieldRules.length > 0) {
      rules[key] = fieldRules
    }
  })
  
  return rules
})

// 字段相关方法
const isFieldRequired = (key: string): boolean => {
  return props.config.schema.required?.includes(key) || false
}

const isFieldVisible = (key: string, field: JsonSchemaProperty): boolean => {
  const uiConfig = props.config.uiSchema?.[key]
  return uiConfig?.['ui:hidden'] !== true
}

const isFieldDisabled = (key: string, field: JsonSchemaProperty): boolean => {
  const uiConfig = props.config.uiSchema?.[key]
  return props.config.disabled || uiConfig?.['ui:disabled'] || false
}

const isFieldReadonly = (key: string, field: JsonSchemaProperty): boolean => {
  const uiConfig = props.config.uiSchema?.[key]
  return props.config.readonly || uiConfig?.['ui:readonly'] || false
}

const getFieldWidget = (key: string, field: JsonSchemaProperty): string => {
  const uiConfig = props.config.uiSchema?.[key]
  if (uiConfig?.['ui:widget']) {
    return uiConfig['ui:widget']
  }
  
  // 根据字段类型和格式推断组件类型
  if (field.type === 'string') {
    if (field.format === 'date') return 'date'
    if (field.format === 'time') return 'time'
    if (field.format === 'date-time') return 'datetime'
    if (field.format === 'color') return 'color'
    if (field.format === 'email' || field.format === 'uri') return 'input'
    if (field.enum) return 'select'
    if (field.maxLength && field.maxLength > 100) return 'textarea'
    return 'input'
  }
  
  if (field.type === 'number' || field.type === 'integer') {
    return 'number'
  }
  
  if (field.type === 'boolean') {
    return 'switch'
  }
  
  return 'input'
}

const getFieldPlaceholder = (key: string, field: JsonSchemaProperty): string => {
  const uiConfig = props.config.uiSchema?.[key]
  return uiConfig?.['ui:placeholder'] || `请输入${field.title || key}`
}

const getFieldHelp = (key: string): string => {
  const uiConfig = props.config.uiSchema?.[key]
  return uiConfig?.['ui:help'] || ''
}

const getFieldOption = (key: string, optionKey: string, defaultValue?: any): any => {
  const uiConfig = props.config.uiSchema?.[key]
  return uiConfig?.['ui:options']?.[optionKey] ?? defaultValue
}

const getFieldOptions = (key: string, field: JsonSchemaProperty): Array<{ label: string; value: any; disabled?: boolean }> => {
  if (field.enum) {
    return field.enum.map(value => ({
      label: String(value),
      value,
      disabled: false
    }))
  }
  return getFieldOption(key, 'options', [])
}

const getValidationTrigger = (key: string, field: JsonSchemaProperty): string => {
  const widget = getFieldWidget(key, field)
  if (['select', 'radio', 'checkbox', 'switch', 'date', 'time', 'datetime'].includes(widget)) {
    return 'change'
  }
  return 'blur'
}

const getCustomComponent = (key: string): any => {
  const componentName = getFieldOption(key, 'component')
  const component = customComponents.get(componentName)
  return component?.component || 'div'
}

const getNestedUiSchema = (key: string, subKey?: string): UiSchema => {
  const uiConfig = props.config.uiSchema?.[key]
  if (subKey && uiConfig?.[subKey]) {
    return uiConfig[subKey]
  }
  return uiConfig || {}
}

const getArrayItemWidget = (key: string, field: JsonSchemaProperty): any => {
  if (field.items) {
    return getFieldWidget(`${key}[]`, field.items)
  }
  return 'el-input'
}

// 数组操作方法
const addArrayItem = (key: string, field: JsonSchemaProperty) => {
  if (!formData.value[key]) {
    formData.value[key] = []
  }
  
  let defaultValue: any
  if (field.items?.type === 'object') {
    defaultValue = {}
  } else if (field.items?.default !== undefined) {
    defaultValue = field.items.default
  } else {
    defaultValue = ''
  }
  
  formData.value[key].push(defaultValue)
}

const removeArrayItem = (key: string, index: number) => {
  formData.value[key].splice(index, 1)
}

// 表单操作方法
const handleSubmit = async () => {
  try {
    const valid = await formRef.value?.validate()
    if (valid) {
      emit('update:modelValue', { ...formData.value })
      emit('submit', { ...formData.value })
      props.config.onSubmit?.(formData.value)
    }
    emit('validate', !!valid, null)
    props.config.onValidate?.(!!valid, null)
  } catch (errors) {
    emit('validate', false, errors)
    props.config.onValidate?.(false, errors)
  }
}

const handleReset = () => {
  formRef.value?.resetFields()
  initFormData()
  emit('reset')
  props.config.onReset?.()
}

// 初始化表单数据
const initFormData = () => {
  const data: any = {}
  
  // 设置默认值
  Object.entries(props.config.schema.properties || {}).forEach(([key, field]) => {
    if (field.default !== undefined) {
      data[key] = field.default
    } else if (field.type === 'array') {
      data[key] = []
    } else if (field.type === 'object') {
      data[key] = {}
    }
  })
  
  // 合并传入的数据
  if (props.config.formData) {
    Object.assign(data, props.config.formData)
  }
  
  if (props.modelValue) {
    Object.assign(data, props.modelValue)
  }
  
  formData.value = data
}

// 监听数据变化
watch(() => formData.value, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  if (newValue && newValue !== formData.value) {
    formData.value = { ...newValue }
  }
}, { deep: true })

watch(() => props.config, () => {
  initFormData()
}, { deep: true })

// 生命周期
onMounted(() => {
  initFormData()
})
</script>

<style scoped>
.json-schema-form {
  @apply space-y-4;
}

.nested-form {
  @apply border border-gray-200 rounded-lg p-4;
}

.array-field {
  @apply space-y-2;
}

.array-item {
  @apply flex items-start gap-2 p-3 border border-gray-200 rounded;
}

.field-help {
  @apply text-sm text-gray-500 mt-1;
}

.form-actions {
  @apply flex gap-2 pt-4 border-t border-gray-200;
}

:deep(.el-form-item__label) {
  @apply font-medium text-gray-700;
}

:deep(.el-form-item__error) {
  @apply text-red-500 text-sm;
}
</style>
