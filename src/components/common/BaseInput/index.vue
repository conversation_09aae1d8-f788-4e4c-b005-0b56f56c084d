<template>
  <div class="base-input">
    <label v-if="label" :for="inputId" class="base-input__label">
      {{ label }}
      <span v-if="required" class="base-input__required">*</span>
    </label>
    
    <input
      :id="inputId"
      :type="type"
      :value="modelValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :readonly="readonly"
      :class="inputClasses"
      @input="handleInput"
      @blur="handleBlur"
      @focus="handleFocus"
    />
    
    <div v-if="errorMessage" class="base-input__error">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  disabled: {
    type: <PERSON><PERSON><PERSON>,
    default: false
  },
  readonly: {
    type: <PERSON><PERSON><PERSON>,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue', 'blur', 'focus'])

const inputId = ref(`input-${Math.random().toString(36).substr(2, 9)}`)

const inputClasses = computed(() => [
  'base-input__field',
  `base-input__field--${props.size}`,
  {
    'base-input__field--disabled': props.disabled,
    'base-input__field--error': props.errorMessage
  }
])

function handleInput(event) {
  emit('update:modelValue', event.target.value)
}

function handleBlur(event) {
  emit('blur', event)
}

function handleFocus(event) {
  emit('focus', event)
}
</script>

<style lang="scss" scoped>
.base-input {
  display: flex;
  flex-direction: column;
  gap: 4px;
  
  &__label {
    font-size: 14px;
    font-weight: 500;
    color: #262626;
  }
  
  &__required {
    color: #ff4d4f;
  }
  
  &__field {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-size: 14px;
    
    &:focus {
      outline: none;
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
    
    &--small {
      padding: 4px 8px;
      font-size: 12px;
    }
    
    &--medium {
      padding: 8px 12px;
      font-size: 14px;
    }
    
    &--large {
      padding: 12px 16px;
      font-size: 16px;
    }
    
    &--disabled {
      background-color: #f5f5f5;
      color: #bfbfbf;
      cursor: not-allowed;
    }
    
    &--error {
      border-color: #ff4d4f;
      
      &:focus {
        border-color: #ff4d4f;
        box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
      }
    }
  }
  
  &__error {
    font-size: 12px;
    color: #ff4d4f;
  }
}
</style>
