<template>
  <button 
    :class="buttonClasses" 
    :disabled="disabled"
    @click="handleClick"
  >
    <slot></slot>
  </button>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary', 'success', 'warning', 'danger'].includes(value)
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const buttonClasses = computed(() => [
  'base-button',
  `base-button--${props.type}`,
  `base-button--${props.size}`,
  {
    'base-button--disabled': props.disabled
  }
])

function handleClick(event) {
  if (!props.disabled) {
    emit('click', event)
  }
}
</script>

<style lang="scss" scoped>
@use 'sass:map';
@import '@/styles/abstracts/_colors.scss';

.base-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &--small {
    padding: 4px 12px;
    font-size: 12px;
  }
  
  &--medium {
    padding: 8px 16px;
    font-size: 14px;
  }
  
  &--large {
    padding: 12px 20px;
    font-size: 16px;
  }
  
  &--default {
    background-color: map.get($gray-colors, 'by1');
    border-color: map.get($gray-colors, 'by4');
    color: map.get($gray-colors, 'by8');
    
    &:hover {
      border-color: $color-bu5;
      color: map.get($brand-colors, 'blue-main');
    }
  }
  
  &--primary {
    background-color: map.get($brand-colors, 'blue-main');
    border-color: map.get($brand-colors, 'blue-main');
    color: map.get($gray-colors, 'by1');
    
    &:hover {
      background-color: $color-bu6;
      border-color: $color-bu6;
    }
  }
  
  &--success {
    background-color: map.get($brand-colors, 'green-main');
    border-color: map.get($brand-colors, 'green-main');
    color: map.get($gray-colors, 'by1');
    
    &:hover {
      background-color: $color-gn6;
      border-color: $color-gn6;
    }
  }
  
  &--warning {
    background-color: map.get($brand-colors, 'orange-main');
    border-color: map.get($brand-colors, 'orange-main');
    color: map.get($gray-colors, 'by1');
    
    &:hover {
      background-color: $color-oe5;
      border-color: $color-oe5;
    }
  }
  
  &--danger {
    background-color: map.get($brand-colors, 'red-main');
    border-color: map.get($brand-colors, 'red-main');
    color: map.get($gray-colors, 'by1');
    
    &:hover {
      background-color: $color-rd6;
      border-color: $color-rd6;
    }
  }
  
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:hover {
      // 禁用状态不响应hover
    }
  }
}
</style>