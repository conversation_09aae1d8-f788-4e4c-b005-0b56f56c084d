<template>
  <div class="data-grid">
    <!-- 高级筛选区 -->
    <div v-if="advancedFilter?.enabled" class="data-grid__filter">
      <AdvancedFilter
        v-model="filterData"
        :config="advancedFilter"
        :collapsed="filterCollapsed"
        @search="handleSearch"
        @reset="handleResetSearch"
        @toggle="handleFilterToggle"
      />
    </div>

    <!-- 表格区域 -->
    <div class="data-grid__table">
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tableData"
        v-bind="mergedTableProps"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        @filter-change="handleFilterChange"
        @row-click="handleRowClick"
        @row-dblclick="handleRowDblClick"
      >
        <!-- 动态列 -->
        <el-table-column
          v-for="column in processedColumns"
          :key="column.prop"
          v-bind="getColumnProps(column)"
        >
          <!-- 自定义表头 -->
          <template v-if="column.renderHeader" #header="{ column: col }">
            <component :is="column.renderHeader(column)" />
          </template>

          <!-- 自定义单元格内容 -->
          <template #default="{ row, column: col, $index }">
            <component
              v-if="column.renderCell"
              :is="column.renderCell({ row, column, $index })"
            />
            <span v-else-if="column.type === 'boolean'">
              {{ formatBoolean(getCellValue(row, column.prop)) }}
            </span>
            <span v-else-if="column.type === 'date'">
              {{ formatDate(getCellValue(row, column.prop)) }}
            </span>
            <span v-else-if="column.type === 'number'">
              {{ formatNumber(getCellValue(row, column.prop)) }}
            </span>
            <span v-else>
              {{ getCellValue(row, column.prop) }}
            </span>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column
          v-if="actionColumn"
          :label="actionColumn.label || '操作'"
          :width="actionColumn.width"
          :fixed="actionColumn.fixed"
          class-name="data-grid__action-column"
        >
          <template #default="{ row, $index }">
            <div class="data-grid__actions">
              <template v-for="action in getVisibleActions(row)" :key="action.text">
                <el-button
                  :type="action.type || 'text'"
                  :size="action.size || 'small'"
                  :disabled="getActionDisabled(action, row)"
                  :icon="action.icon"
                  @click="action.onClick(row, $index)"
                >
                  {{ action.text }}
                </el-button>
              </template>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div v-if="pagination?.enabled" class="data-grid__pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="currentPageSize"
        :page-sizes="pagination.pageSizes || [10, 20, 50, 100]"
        :layout="pagination.layout || 'total, sizes, prev, pager, next, jumper'"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, provide } from 'vue'
import { ElTable, ElTableColumn, ElButton, ElPagination } from 'element-plus'
import type {
  DataGridConfig,
  DataGridColumn,
  ActionColumnConfig,
  DataSourceConfig,
  DataGridInstance,
  ActionButtonConfig
} from '@/types/data-grid'
import AdvancedFilter from './AdvancedFilter.vue'
import { useDataSource } from '@/composables/useDataSource'
import { formatDate, formatNumber, formatBoolean } from '@/utils/format'

interface Props {
  config: DataGridConfig
}

const props = defineProps<Props>()

// 响应式数据
const tableRef = ref<InstanceType<typeof ElTable>>()
const loading = ref(false)
const tableData = ref<any[]>([])
const total = ref(0)
const currentPage = ref(1)
const currentPageSize = ref(props.config.dataSource.pagination?.pageSize || 20)
const filterData = ref<Record<string, any>>({})
const filterCollapsed = ref(props.config.advancedFilter?.defaultCollapsed ?? true)
const sortParams = ref<{ prop: string; order: string }>()

// 计算属性
const { columns, dataSource, actionColumn, advancedFilter, tableProps, events } = props.config

const processedColumns = computed(() => {
  return columns.filter(column => column.type !== 'action')
})

const pagination = computed(() => dataSource.pagination)

const mergedTableProps = computed(() => ({
  stripe: true,
  border: true,
  size: 'default',
  ...tableProps
}))

// 数据源处理
const { fetchData, searchData, resetSearchData } = useDataSource(dataSource)

// 获取列属性
const getColumnProps = (column: DataGridColumn) => {
  const props: Record<string, any> = { ...column }
  delete props.renderHeader
  delete props.renderCell
  delete props.formConfig
  return props
}

// 获取单元格值
const getCellValue = (row: any, prop: string) => {
  return prop.split('.').reduce((obj, key) => obj?.[key], row)
}

// 获取可见的操作按钮
const getVisibleActions = (row: any) => {
  if (!actionColumn?.actions) return []
  return actionColumn.actions.filter(action => {
    if (typeof action.visible === 'function') {
      return action.visible(row)
    }
    return action.visible !== false
  })
}

// 获取操作按钮是否禁用
const getActionDisabled = (action: ActionButtonConfig, row: any) => {
  if (typeof action.disabled === 'function') {
    return action.disabled(row)
  }
  return action.disabled || false
}

// 事件处理
const handleSelectionChange = (selection: any[]) => {
  events?.onSelectionChange?.(selection)
}

const handleSortChange = (params: { column: any; prop: string; order: string }) => {
  sortParams.value = params
  events?.onSortChange?.(params)
  loadData()
}

const handleFilterChange = (filters: Record<string, any[]>) => {
  events?.onFilterChange?.(filters)
  loadData()
}

const handleRowClick = (row: any, column: any, event: Event) => {
  events?.onRowClick?.(row, column, event)
}

const handleRowDblClick = (row: any, column: any, event: Event) => {
  events?.onRowDblClick?.(row, column, event)
}

const handleSearch = (searchParams: Record<string, any>) => {
  filterData.value = searchParams
  currentPage.value = 1
  loadData()
}

const handleResetSearch = () => {
  filterData.value = {}
  currentPage.value = 1
  loadData()
}

const handleFilterToggle = (collapsed: boolean) => {
  filterCollapsed.value = collapsed
}

const handleSizeChange = (size: number) => {
  currentPageSize.value = size
  currentPage.value = 1
  loadData()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadData()
}

// 数据加载
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: currentPageSize.value,
      ...filterData.value,
      ...sortParams.value
    }

    const result = await fetchData(params)
    
    if (dataSource.remote) {
      tableData.value = result.data
      total.value = result.total
    } else {
      // 本地数据处理分页、排序、筛选
      let processedData = [...(dataSource.data || [])]
      
      // 筛选
      if (Object.keys(filterData.value).length > 0) {
        processedData = processedData.filter(row => {
          return Object.entries(filterData.value).every(([key, value]) => {
            if (value === undefined || value === null || value === '') return true
            const cellValue = getCellValue(row, key)
            return String(cellValue).toLowerCase().includes(String(value).toLowerCase())
          })
        })
      }
      
      // 排序
      if (sortParams.value?.prop) {
        const { prop, order } = sortParams.value
        processedData.sort((a, b) => {
          const aVal = getCellValue(a, prop)
          const bVal = getCellValue(b, prop)
          const result = aVal > bVal ? 1 : aVal < bVal ? -1 : 0
          return order === 'ascending' ? result : -result
        })
      }
      
      total.value = processedData.length
      
      // 分页
      if (pagination.value?.enabled) {
        const start = (currentPage.value - 1) * currentPageSize.value
        const end = start + currentPageSize.value
        tableData.value = processedData.slice(start, end)
      } else {
        tableData.value = processedData
      }
    }
  } catch (error) {
    console.error('Failed to load data:', error)
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 对外暴露的方法
const refresh = () => {
  loadData()
}

const search = (params?: Record<string, any>) => {
  if (params) {
    filterData.value = { ...filterData.value, ...params }
  }
  currentPage.value = 1
  loadData()
}

const resetSearch = () => {
  filterData.value = {}
  currentPage.value = 1
  loadData()
}

const clearSelection = () => {
  tableRef.value?.clearSelection()
}

const toggleRowSelection = (row: any, selected?: boolean) => {
  tableRef.value?.toggleRowSelection(row, selected)
}

const toggleAllSelection = () => {
  tableRef.value?.toggleAllSelection()
}

const getSelectionRows = () => {
  return tableRef.value?.getSelectionRows() || []
}

const clearSort = () => {
  tableRef.value?.clearSort()
  sortParams.value = undefined
}

const sort = (prop: string, order: 'ascending' | 'descending') => {
  tableRef.value?.sort(prop, order)
}

const clearFilter = (columnKeys?: string[]) => {
  tableRef.value?.clearFilter(columnKeys)
}

const scrollTo = (options: { top?: number; left?: number }) => {
  tableRef.value?.scrollTo(options)
}

const setCurrentRow = (row: any) => {
  tableRef.value?.setCurrentRow(row)
}

const exportData = (format: 'excel' | 'csv', options?: any) => {
  // TODO: 实现数据导出功能
  console.log('Export data:', format, options)
}

// 提供实例方法
const instance: DataGridInstance = {
  refresh,
  search,
  resetSearch,
  clearSelection,
  toggleRowSelection,
  toggleAllSelection,
  getSelectionRows,
  clearSort,
  sort,
  clearFilter,
  scrollTo,
  setCurrentRow,
  exportData
}

provide('dataGridInstance', instance)
defineExpose(instance)

// 生命周期
onMounted(() => {
  loadData()
})

// 监听配置变化
watch(() => props.config, () => {
  currentPage.value = 1
  loadData()
}, { deep: true })
</script>

<style lang="scss" scoped>
@import '@/styles/abstracts/_colors.scss';
@import '@/styles/abstracts/_mixins.scss';

.data-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
  
  &__filter {
    background-color: var(--color-by2);
    padding: 1rem;
    border-radius: 0.5rem;
    @include card-shadow;
  }
  
  &__table {
    background-color: var(--color-by1);
    border-radius: 0.5rem;
    overflow: hidden;
    @include card-shadow;
    
    :deep(.el-table) {
      --el-table-header-bg-color: var(--color-bu1);
      --el-table-border-color: var(--color-by3);
      --el-table-header-text-color: var(--color-by7);
      
      th {
        font-weight: 600;
      }
      
      .el-table__row:hover > td {
        background-color: var(--color-bu1);
      }
      
      .el-button--text {
        color: var(--color-bu7);
        
        &:hover {
          color: var(--color-bu6);
        }
      }
    }
  }
  
  &__actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
  }
  
  &__pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 1rem;
    
    :deep(.el-pagination) {
      --el-pagination-button-color: var(--color-by7);
      --el-pagination-hover-color: var(--color-bu7);
      
      .is-active {
        background-color: var(--color-bu7);
        color: var(--color-by1);
      }
    }
  }
  
  :deep(.data-grid__action-column .cell) {
    display: flex;
    justify-content: center;
  }
}
</style>
