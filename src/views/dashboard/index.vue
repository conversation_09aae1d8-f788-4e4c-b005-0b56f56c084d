<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <el-col :span="24">
        <h1 class="dashboard__title">数据看板</h1>
      </el-col>
    </el-row>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24" v-for="stat in stats" :key="stat.title">
        <el-card class="stat-card" :class="stat.cardClass">
          <div class="stat-content">
            <div class="stat-icon" :class="stat.iconClass">
              <component :is="stat.icon" />
            </div>
            <div class="stat-text">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-title">{{ stat.title }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xl="12" :lg="12" :md="24" :sm="24" :xs="24">
        <el-card title="销售趋势" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>销售趋势</span>
            </div>
          </template>
          <LineChart />
        </el-card>
      </el-col>
      <el-col :xl="12" :lg="12" :md="24" :sm="24" :xs="24">
        <el-card title="产品分布" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>产品分布</span>
            </div>
          </template>
          <PieChart />
        </el-card>
      </el-col>
    </el-row>

    <!-- 表格区域 -->
    <el-row :gutter="20" class="table-row">
      <el-col :span="24">
        <el-card title="最新订单" class="table-card">
          <template #header>
            <div class="card-header">
              <span>最新订单</span>
            </div>
          </template>
          <DataTable />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { User, ShoppingBag, TrendCharts, Wallet } from '@element-plus/icons-vue'
import LineChart from './components/LineChart.vue'
import PieChart from './components/PieChart.vue'
import DataTable from './components/DataTable.vue'

const stats = ref([
  {
    title: '总用户数',
    value: '12,345',
    icon: User,
    iconClass: 'icon-blue',
    cardClass: 'gradient-card--blue'
  },
  {
    title: '订单总数',
    value: '8,976',
    icon: ShoppingBag,
    iconClass: 'icon-green',
    cardClass: 'gradient-card--green'
  },
  {
    title: '销售额',
    value: '¥156,789',
    icon: Wallet,
    iconClass: 'icon-orange',
    cardClass: 'gradient-card--orange'
  },
  {
    title: '增长率',
    value: '+12.5%',
    icon: TrendCharts,
    iconClass: 'icon-red',
    cardClass: 'gradient-card--red'
  }
])
</script>

<style lang="scss" scoped>
@use '@/styles/abstracts/colors' as *;
@use '@/styles/abstracts/mixins' as *;

.dashboard {
  padding: 24px;

  &__title {
    margin-bottom: 24px;
    color: $text-color-primary;
    font-weight: 600;
    font-size: 28px;
  }

  .stats-row {
    margin-bottom: 24px;
  }

  .stat-card {
    margin-bottom: 24px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    overflow: hidden;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
    }
    
    &.gradient-card--blue {
      background: var(--gradient-blue-light);
      .stat-icon.icon-blue {
        background: var(--gradient-blue-deep);
      }
      .stat-value {
        color: $color-bu7;
      }
    }
    
    &.gradient-card--green {
      background: var(--gradient-green-light);
      .stat-icon.icon-green {
        background: var(--gradient-green-deep);
      }
      .stat-value {
        color: $color-gn7;
      }
    }
    
    &.gradient-card--orange {
      background: var(--gradient-orange-light);
      .stat-icon.icon-orange {
        background: var(--gradient-orange-deep);
      }
      .stat-value {
        color: $color-oe7;
      }
    }
    
    &.gradient-card--red {
      background: var(--gradient-red-light);
      .stat-icon.icon-red {
        background: var(--gradient-red-deep);
      }
      .stat-value {
        color: $color-rd7;
      }
    }

    .stat-content {
      display: flex;
      align-items: center;
      padding: 8px;

      .stat-icon {
        width: 56px;
        height: 56px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        color: $color-by1;
        font-size: 24px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      .stat-text {
        .stat-value {
          font-size: 28px;
          font-weight: 700;
          margin-bottom: 6px;
          line-height: 1.2;
        }

        .stat-title {
          font-size: 14px;
          color: $text-color-secondary;
          font-weight: 500;
        }
      }
    }
  }

  .charts-row {
    margin-bottom: 24px;
  }

  .chart-card,
  .table-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    }
    
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 0;
      
      span {
        font-size: 16px;
        font-weight: 600;
        color: $text-color-primary;
      }
    }
  }
}
</style>