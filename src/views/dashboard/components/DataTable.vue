<template>
  <el-table 
    :data="tableData" 
    style="width: 100%"
    stripe
    border
    :default-sort="{ prop: 'orderDate', order: 'descending' }"
    class="galaxy-table"
  >
    <el-table-column 
      prop="orderId" 
      label="订单号" 
      width="140"
      fixed="left"
    />
    <el-table-column 
      prop="customerName" 
      label="客户姓名" 
      width="120"
    />
    <el-table-column 
      prop="product" 
      label="产品" 
      width="160"
    />
    <el-table-column 
      prop="amount" 
      label="金额" 
      width="120"
      sortable
    >
      <template #default="scope">
        <span class="amount-value">
          ¥{{ scope.row.amount.toLocaleString() }}
        </span>
      </template>
    </el-table-column>
    <el-table-column 
      prop="status" 
      label="状态" 
      width="100"
    >
      <template #default="scope">
        <el-tag 
          :type="getStatusType(scope.row.status)"
          size="small"
          :class="`status-tag status-tag--${scope.row.status}`"
        >
          {{ getStatusText(scope.row.status) }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column 
      prop="orderDate" 
      label="订单日期" 
      width="120"
      sortable
    >
      <template #default="scope">
        {{ formatDate(scope.row.orderDate) }}
      </template>
    </el-table-column>
    <el-table-column 
      label="操作" 
      width="120"
      fixed="right"
    >
      <template #default="scope">
        <el-button 
          type="primary" 
          size="small"
          class="action-button"
          @click="handleView(scope.row)"
        >
          查看详情
        </el-button>
      </template>
    </el-table-column>
  </el-table>
  
  <div class="pagination-wrapper">
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const allData = ref([])

const tableData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return allData.value.slice(start, end)
})

const generateMockData = () => {
  const customers = ['张三', '李四', '王五', '赵六', '孙七', '周八', '吴九', '郑十']
  const products = ['iPhone 14', 'MacBook Pro', 'iPad Air', 'AirPods Pro', 'Apple Watch', 'iMac', 'Mac Studio', 'HomePod']
  const statuses = ['pending', 'processing', 'completed', 'cancelled']
  
  const data = []
  for (let i = 1; i <= 50; i++) {
    data.push({
      orderId: `ORD${String(i).padStart(6, '0')}`,
      customerName: customers[Math.floor(Math.random() * customers.length)],
      product: products[Math.floor(Math.random() * products.length)],
      amount: Math.floor(Math.random() * 10000) + 1000,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      orderDate: new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000).toISOString()
    })
  }
  return data
}

const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'processing': 'primary',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString()
}

const handleView = (row) => {
  ElMessage({
    message: `查看订单: ${row.orderId}`,
    type: 'info'
  })
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

onMounted(() => {
  allData.value = generateMockData()
  total.value = allData.value.length
})
</script>

<style lang="scss" scoped>
@use '@/styles/abstracts/colors' as *;
@use '@/styles/abstracts/mixins' as *;

.galaxy-table {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  
  .amount-value {
    color: $color-blue-main;
    font-weight: 600;
  }

  .status-tag {
    text-transform: capitalize;
    font-weight: 500;
    
    &--pending {
      background-color: var(--color-oe2);
      color: var(--color-oe7);
      border-color: var(--color-oe4);
    }
    
    &--processing {
      background-color: var(--color-bu2);
      color: var(--color-bu7);
      border-color: var(--color-bu4); 
    }
    
    &--completed {
      background-color: var(--color-gn2);
      color: var(--color-gn7);
      border-color: var(--color-gn4);
    }
    
    &--cancelled {
      background-color: var(--color-rd2);
      color: var(--color-rd7);
      border-color: var(--color-rd4);
    }
  }

  .action-button {
    background: var(--gradient-blue-deep);
    border: none;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

.pagination-wrapper {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}
</style>