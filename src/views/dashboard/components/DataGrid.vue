<template>
  <div class="ag-theme-alpine" style="height: 400px; width: 100%">
    <ag-grid-vue
      :columnDefs="columnDefs"
      :rowData="rowData"
      :defaultColDef="defaultColDef"
      :pagination="true"
      :paginationPageSize="10"
      @grid-ready="onGridReady"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { AgGridVue } from 'ag-grid-vue3'
import 'ag-grid-community/styles/ag-grid.css'
import 'ag-grid-community/styles/ag-theme-alpine.css'

let gridApi = null

const columnDefs = ref([
  {
    field: 'orderId',
    headerName: '订单号',
    width: 120,
    pinned: 'left'
  },
  {
    field: 'customerName',
    headerName: '客户姓名',
    width: 120
  },
  {
    field: 'product',
    headerName: '产品',
    width: 150
  },
  {
    field: 'amount',
    headerName: '金额',
    width: 100,
    valueFormatter: params => `¥${params.value.toLocaleString()}`
  },
  {
    field: 'status',
    headerName: '状态',
    width: 100,
    cellRenderer: params => {
      const status = params.value
      const statusMap = {
        'pending': { text: '待处理', class: 'status-pending' },
        'processing': { text: '处理中', class: 'status-processing' },
        'completed': { text: '已完成', class: 'status-completed' },
        'cancelled': { text: '已取消', class: 'status-cancelled' }
      }
      const statusInfo = statusMap[status] || { text: status, class: '' }
      return `<span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>`
    }
  },
  {
    field: 'orderDate',
    headerName: '订单日期',
    width: 120,
    valueFormatter: params => new Date(params.value).toLocaleDateString()
  },
  {
    field: 'actions',
    headerName: '操作',
    width: 120,
    cellRenderer: () => {
      return '<button class="action-btn">查看详情</button>'
    }
  }
])

const defaultColDef = ref({
  sortable: true,
  filter: true,
  resizable: true
})

const rowData = ref([])

const generateMockData = () => {
  const customers = ['张三', '李四', '王五', '赵六', '孙七', '周八', '吴九', '郑十']
  const products = ['iPhone 14', 'MacBook Pro', 'iPad Air', 'AirPods Pro', 'Apple Watch', 'iMac', 'Mac Studio', 'HomePod']
  const statuses = ['pending', 'processing', 'completed', 'cancelled']
  
  const data = []
  for (let i = 1; i <= 50; i++) {
    data.push({
      orderId: `ORD${String(i).padStart(6, '0')}`,
      customerName: customers[Math.floor(Math.random() * customers.length)],
      product: products[Math.floor(Math.random() * products.length)],
      amount: Math.floor(Math.random() * 10000) + 1000,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      orderDate: new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000).toISOString()
    })
  }
  return data
}

const onGridReady = (params) => {
  gridApi = params.api
}

onMounted(() => {
  rowData.value = generateMockData()
})
</script>

<style lang="scss" scoped>
:deep(.ag-theme-alpine) {
  --ag-border-color: #e4e7ed;
  --ag-header-background-color: #f5f7fa;
  --ag-header-foreground-color: #303133;
  --ag-odd-row-background-color: #fafbfc;
  
  .status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    
    &.status-pending {
      background-color: #fdf6ec;
      color: #e6a23c;
    }
    
    &.status-processing {
      background-color: #f0f9ff;
      color: #409eff;
    }
    
    &.status-completed {
      background-color: #f0f9e8;
      color: #67c23a;
    }
    
    &.status-cancelled {
      background-color: #fef0f0;
      color: #f56c6c;
    }
  }
  
  .action-btn {
    background-color: #409eff;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    
    &:hover {
      background-color: #337ecc;
    }
  }
}
</style>
