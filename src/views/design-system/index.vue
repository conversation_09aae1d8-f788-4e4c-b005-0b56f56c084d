<template>
  <div class="design-system">
    <!-- Header -->
    <header class="ds-header">
      <div class="container">
        <h1 class="gradient-text--galaxy-blue">Galaxy Design System v3.0</h1>
        <p class="ds-subtitle">
          基于 Element Plus 的企业级设计系统，提供完整的设计令牌、组件库和使用指南
        </p>
      </div>
    </header>

    <!-- Navigation -->
    <nav class="ds-nav">
      <div class="container">
        <el-menu
          :default-active="activeSection"
          mode="horizontal"
          @select="scrollToSection"
          class="ds-menu"
        >
          <el-menu-item index="colors">颜色系统</el-menu-item>
          <el-menu-item index="typography">字体系统</el-menu-item>
          <el-menu-item index="spacing">间距系统</el-menu-item>
          <el-menu-item index="components">组件展示</el-menu-item>
          <el-menu-item index="gradients">渐变系统</el-menu-item>
          <el-menu-item index="status">状态组件</el-menu-item>
        </el-menu>
      </div>
    </nav>

    <!-- Content -->
    <main class="ds-content">
      <div class="container">
        <!-- Colors Section -->
        <section id="colors" class="ds-section">
          <h2>颜色系统</h2>
          <p class="section-description">
            Galaxy Design System 提供了完整的颜色调色板，包括品牌色、功能色、语义色等。
          </p>

          <!-- Brand Colors -->
          <div class="color-group">
            <h3>品牌主色</h3>
            <div class="color-palette">
              <div class="color-item">
                <div class="color-swatch" style="background-color: var(--color-blue-main)"></div>
                <div class="color-info">
                  <span class="color-name">主蓝色</span>
                  <span class="color-value">#434DBF</span>
                  <span class="color-var">--color-blue-main</span>
                </div>
              </div>
              <div class="color-item">
                <div class="color-swatch" style="background-color: var(--color-orange-main)"></div>
                <div class="color-info">
                  <span class="color-name">主橙色</span>
                  <span class="color-value">#EBA21E</span>
                  <span class="color-var">--color-orange-main</span>
                </div>
              </div>
              <div class="color-item">
                <div class="color-swatch" style="background-color: var(--color-red-main)"></div>
                <div class="color-info">
                  <span class="color-name">主红色</span>
                  <span class="color-value">#D94C4C</span>
                  <span class="color-var">--color-red-main</span>
                </div>
              </div>
              <div class="color-item">
                <div class="color-swatch" style="background-color: var(--color-green-main)"></div>
                <div class="color-info">
                  <span class="color-name">主绿色</span>
                  <span class="color-value">#248360</span>
                  <span class="color-var">--color-green-main</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Blue Palette -->
          <div class="color-group">
            <h3>蓝色调色板</h3>
            <div class="color-palette">
              <div class="color-item" v-for="(color, index) in blueColors" :key="index">
                <div
                  class="color-swatch"
                  :style="`background-color: var(--color-bu${color.level})`"
                ></div>
                <div class="color-info">
                  <span class="color-name">{{ color.name }}</span>
                  <span class="color-value">{{ color.value }}</span>
                  <span class="color-var">--color-bu{{ color.level }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Gray Scale -->
          <div class="color-group">
            <h3>灰度色阶</h3>
            <div class="color-palette">
              <div class="color-item" v-for="(color, index) in grayColors" :key="index">
                <div
                  class="color-swatch"
                  :style="`background-color: var(--color-by${color.level})`"
                ></div>
                <div class="color-info">
                  <span class="color-name">{{ color.name }}</span>
                  <span class="color-value">{{ color.value }}</span>
                  <span class="color-var">--color-by{{ color.level }}</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Typography Section -->
        <section id="typography" class="ds-section">
          <h2>字体系统</h2>
          <p class="section-description">
            完整的字体层级系统，支持中英文混排，提供多种字重和尺寸选择。
          </p>

          <!-- Font Families -->
          <div class="typography-group">
            <h3>字体族</h3>
            <div class="font-families">
              <div class="font-family-item">
                <div class="font-sample" style="font-family: var(--font-family-chinese)">
                  中文字体 Chinese Font 123
                </div>
                <span class="font-name">中文字体</span>
                <span class="font-var">--font-family-chinese</span>
              </div>
              <div class="font-family-item">
                <div class="font-sample" style="font-family: var(--font-family-english)">
                  English Font 英文字体 123
                </div>
                <span class="font-name">英文字体</span>
                <span class="font-var">--font-family-english</span>
              </div>
              <div class="font-family-item">
                <div class="font-sample" style="font-family: var(--font-family-numeric)">
                  Numeric Font 1234567890
                </div>
                <span class="font-name">数字字体</span>
                <span class="font-var">--font-family-numeric</span>
              </div>
              <div class="font-family-item">
                <div class="font-sample" style="font-family: var(--font-family-mono)">
                  Monospace Font 等宽字体 123
                </div>
                <span class="font-name">等宽字体</span>
                <span class="font-var">--font-family-mono</span>
              </div>
            </div>
          </div>

          <!-- Font Sizes -->
          <div class="typography-group">
            <h3>字体尺寸</h3>
            <div class="font-sizes">
              <div class="font-size-item" v-for="size in fontSizes" :key="size.name">
                <div
                  class="font-sample"
                  :style="`font-size: var(--font-size-${size.name}); line-height: var(--line-height-${size.name})`"
                >
                  {{ size.sample }}
                </div>
                <div class="font-info">
                  <span class="font-name">{{ size.label }}</span>
                  <span class="font-value">{{ size.value }}</span>
                  <span class="font-var">--font-size-{{ size.name }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Headings -->
          <div class="typography-group">
            <h3>标题层级</h3>
            <div class="headings">
              <h1
                style="
                  font-size: var(--font-size-6xl);
                  line-height: var(--line-height-6xl);
                  font-weight: var(--font-weight-bold);
                "
              >
                H1 主标题 - 48px
              </h1>
              <h2
                style="
                  font-size: var(--font-size-5xl);
                  line-height: var(--line-height-5xl);
                  font-weight: var(--font-weight-bold);
                "
              >
                H2 副标题 - 36px
              </h2>
              <h3
                style="
                  font-size: var(--font-size-4xl);
                  line-height: var(--line-height-4xl);
                  font-weight: var(--font-weight-semibold);
                "
              >
                H3 三级标题 - 30px
              </h3>
              <h4
                style="
                  font-size: var(--font-size-3xl);
                  line-height: var(--line-height-3xl);
                  font-weight: var(--font-weight-semibold);
                "
              >
                H4 四级标题 - 24px
              </h4>
              <h5
                style="
                  font-size: var(--font-size-2xl);
                  line-height: var(--line-height-2xl);
                  font-weight: var(--font-weight-semibold);
                "
              >
                H5 五级标题 - 20px
              </h5>
              <h6
                style="
                  font-size: var(--font-size-xl);
                  line-height: var(--line-height-xl);
                  font-weight: var(--font-weight-semibold);
                "
              >
                H6 六级标题 - 18px
              </h6>
            </div>
          </div>
        </section>

        <!-- Spacing Section -->
        <section id="spacing" class="ds-section">
          <h2>间距系统</h2>
          <p class="section-description">基于 4px 基础单位的间距系统，提供一致的布局节奏。</p>

          <div class="spacing-group">
            <h3>间距标尺</h3>
            <div class="spacing-scale">
              <div class="spacing-item" v-for="space in spacingScale" :key="space.name">
                <div class="spacing-visual">
                  <div class="spacing-bar" :style="`width: var(--space-${space.name})`"></div>
                </div>
                <div class="spacing-info">
                  <span class="spacing-name">{{ space.label }}</span>
                  <span class="spacing-value">{{ space.value }}</span>
                  <span class="spacing-var">--space-{{ space.name }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="spacing-group">
            <h3>圆角系统</h3>
            <div class="radius-scale">
              <div class="radius-item" v-for="radius in radiusScale" :key="radius.name">
                <div
                  class="radius-visual"
                  :style="`border-radius: var(--radius-${radius.name})`"
                ></div>
                <div class="radius-info">
                  <span class="radius-name">{{ radius.label }}</span>
                  <span class="radius-value">{{ radius.value }}</span>
                  <span class="radius-var">--radius-{{ radius.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Components Section -->
        <section id="components" class="ds-section">
          <h2>Element Plus 组件展示</h2>
          <p class="section-description">
            基于 Galaxy Design System 主题定制的 Element Plus 组件。
          </p>

          <!-- Buttons -->
          <div class="component-group">
            <h3>按钮组件</h3>
            <div class="component-demo">
              <div class="demo-row">
                <el-button type="primary">主要按钮</el-button>
                <el-button>默认按钮</el-button>
                <el-button type="success">成功按钮</el-button>
                <el-button type="warning">警告按钮</el-button>
                <el-button type="danger">危险按钮</el-button>
                <el-button type="info">信息按钮</el-button>
              </div>
              <div class="demo-row">
                <el-button size="large">大型按钮</el-button>
                <el-button>默认按钮</el-button>
                <el-button size="small">小型按钮</el-button>
              </div>
              <div class="demo-row">
                <el-button type="primary" plain>朴素按钮</el-button>
                <el-button type="primary" round>圆角按钮</el-button>
                <el-button type="primary" circle>
                  <el-icon><Plus /></el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <!-- Tags -->
          <div class="component-group">
            <h3>标签组件</h3>
            <div class="component-demo">
              <div class="demo-row">
                <el-tag>默认标签</el-tag>
                <el-tag type="success">成功标签</el-tag>
                <el-tag type="warning">警告标签</el-tag>
                <el-tag type="danger">危险标签</el-tag>
                <el-tag type="info">信息标签</el-tag>
              </div>
              <div class="demo-row">
                <el-tag size="large">大型标签</el-tag>
                <el-tag>默认标签</el-tag>
                <el-tag size="small">小型标签</el-tag>
              </div>
              <div class="demo-row">
                <el-tag effect="dark">深色效果</el-tag>
                <el-tag effect="light">浅色效果</el-tag>
                <el-tag effect="plain">朴素效果</el-tag>
              </div>
            </div>
          </div>

          <!-- Cards -->
          <div class="component-group">
            <h3>卡片组件</h3>
            <div class="component-demo">
              <div class="card-grid">
                <el-card class="demo-card">
                  <template #header>
                    <div class="card-header">
                      <span>基础卡片</span>
                      <el-button type="text">操作按钮</el-button>
                    </div>
                  </template>
                  <div>这是一个基础的卡片组件，展示了 Galaxy Design System 的卡片样式。</div>
                </el-card>

                <el-card class="demo-card" shadow="hover">
                  <template #header>
                    <div class="card-header">
                      <span>悬浮阴影</span>
                    </div>
                  </template>
                  <div>鼠标悬浮时显示阴影效果的卡片。</div>
                </el-card>

                <el-card class="demo-card" shadow="always">
                  <template #header>
                    <div class="card-header">
                      <span>始终阴影</span>
                    </div>
                  </template>
                  <div>始终显示阴影效果的卡片。</div>
                </el-card>
              </div>
            </div>
          </div>
        </section>

        <!-- Gradients Section -->
        <section id="gradients" class="ds-section">
          <h2>渐变系统</h2>
          <p class="section-description">
            Galaxy Design System 提供了丰富的渐变效果，包括品牌渐变、功能渐变等。
          </p>

          <!-- Brand Gradients -->
          <div class="gradient-group">
            <h3>品牌渐变</h3>
            <div class="gradient-palette">
              <div class="gradient-item">
                <div class="gradient-swatch gradient-bg--galaxy-blue"></div>
                <div class="gradient-info">
                  <span class="gradient-name">Galaxy Blue</span>
                  <span class="gradient-var">--gradient-galaxy-blue</span>
                </div>
              </div>
              <div class="gradient-item">
                <div class="gradient-swatch gradient-bg--galaxy-red"></div>
                <div class="gradient-info">
                  <span class="gradient-name">Galaxy Red</span>
                  <span class="gradient-var">--gradient-galaxy-red</span>
                </div>
              </div>
              <div class="gradient-item">
                <div class="gradient-swatch gradient-bg--wealth-gold"></div>
                <div class="gradient-info">
                  <span class="gradient-name">Wealth Gold</span>
                  <span class="gradient-var">--gradient-wealth-gold</span>
                </div>
              </div>
              <div class="gradient-item">
                <div class="gradient-swatch gradient-bg--nature-green"></div>
                <div class="gradient-info">
                  <span class="gradient-name">Nature Green</span>
                  <span class="gradient-var">--gradient-nature-green</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Rainbow Gradients -->
          <div class="gradient-group">
            <h3>彩虹渐变</h3>
            <div class="gradient-palette">
              <div class="gradient-item">
                <div class="gradient-swatch gradient-bg--rainbow-warm"></div>
                <div class="gradient-info">
                  <span class="gradient-name">Rainbow Warm</span>
                  <span class="gradient-var">--gradient-rainbow-warm</span>
                </div>
              </div>
              <div class="gradient-item">
                <div class="gradient-swatch gradient-bg--rainbow-cool"></div>
                <div class="gradient-info">
                  <span class="gradient-name">Rainbow Cool</span>
                  <span class="gradient-var">--gradient-rainbow-cool</span>
                </div>
              </div>
              <div class="gradient-item">
                <div class="gradient-swatch gradient-bg--sunset"></div>
                <div class="gradient-info">
                  <span class="gradient-name">Sunset</span>
                  <span class="gradient-var">--gradient-sunset</span>
                </div>
              </div>
              <div class="gradient-item">
                <div class="gradient-swatch gradient-bg--ocean"></div>
                <div class="gradient-info">
                  <span class="gradient-name">Ocean</span>
                  <span class="gradient-var">--gradient-ocean</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Gradient Text Demo -->
          <div class="gradient-group">
            <h3>渐变文字</h3>
            <div class="gradient-text-demo">
              <div class="gradient-text gradient-text--galaxy-blue">Galaxy Design System</div>
              <div class="gradient-text gradient-text--sunset">Beautiful Gradients</div>
              <div class="gradient-text gradient-text--ocean">Ocean Waves</div>
              <div class="gradient-text gradient-text--rainbow-warm">Rainbow Colors</div>
            </div>
          </div>

          <!-- Gradient Cards Demo -->
          <div class="gradient-group">
            <h3>渐变卡片</h3>
            <div class="gradient-cards-demo">
              <div class="gradient-card gradient-card--blue">
                <h4>深蓝渐变卡片</h4>
                <p>这是一个使用深蓝渐变背景的卡片组件。</p>
              </div>
              <div class="gradient-card gradient-card--green">
                <h4>绿色渐变卡片</h4>
                <p>这是一个使用绿色渐变背景的卡片组件。</p>
              </div>
              <div class="gradient-card gradient-card--orange gradient-card--light">
                <h4>浅橙渐变卡片</h4>
                <p>这是一个使用浅橙渐变背景的卡片组件。</p>
              </div>
            </div>
          </div>
        </section>

        <!-- Status Section -->
        <section id="status" class="ds-section">
          <h2>状态组件</h2>
          <p class="section-description">专为业务场景设计的状态指示器和进度组件。</p>

          <!-- Status Indicators -->
          <div class="status-group">
            <h3>状态指示器</h3>
            <div class="status-demo">
              <div class="status-row">
                <span class="status-indicator status-indicator--in-progress">进行中</span>
                <span class="status-indicator status-indicator--complete">已完成</span>
                <span class="status-indicator status-indicator--pending">待处理</span>
                <span class="status-indicator status-indicator--cancelled">已取消</span>
                <span class="status-indicator status-indicator--draft">草稿</span>
              </div>
              <div class="status-row">
                <span class="status-indicator status-indicator--sm status-indicator--in-progress"
                  >小型</span
                >
                <span class="status-indicator status-indicator--in-progress">默认</span>
                <span class="status-indicator status-indicator--lg status-indicator--in-progress"
                  >大型</span
                >
              </div>
            </div>
          </div>

          <!-- Transaction Status -->
          <div class="status-group">
            <h3>交易状态</h3>
            <div class="status-demo">
              <div class="status-row">
                <span class="transaction-status transaction-status--buy">买入</span>
                <span class="transaction-status transaction-status--sell">卖出</span>
                <span class="transaction-status transaction-status--hold">持有</span>
                <span class="transaction-status transaction-status--cancel">取消</span>
              </div>
            </div>
          </div>

          <!-- Loading States -->
          <div class="status-group">
            <h3>加载状态</h3>
            <div class="loading-demo">
              <div class="loading-row">
                <div class="loading-skeleton loading-skeleton--text"></div>
                <div class="loading-skeleton loading-skeleton--text"></div>
                <div class="loading-skeleton loading-skeleton--text" style="width: 60%"></div>
              </div>
              <div class="loading-row">
                <div class="loading-skeleton loading-skeleton--avatar"></div>
                <div style="flex: 1">
                  <div class="loading-skeleton loading-skeleton--title"></div>
                  <div class="loading-skeleton loading-skeleton--paragraph"></div>
                  <div class="loading-skeleton loading-skeleton--paragraph"></div>
                </div>
              </div>
              <div class="loading-row">
                <div class="loading-skeleton loading-skeleton--card"></div>
              </div>
            </div>
          </div>

          <!-- Animations -->
          <div class="status-group">
            <h3>动画效果</h3>
            <div class="animation-demo">
              <div class="animation-row">
                <div
                  class="pulse"
                  style="
                    width: 60px;
                    height: 60px;
                    background-color: var(--color-blue-main);
                    border-radius: 50%;
                  "
                ></div>
                <div
                  class="heartbeat"
                  style="
                    width: 60px;
                    height: 60px;
                    background-color: var(--color-red-main);
                    border-radius: 50%;
                  "
                ></div>
                <div class="loading-skeleton loading-skeleton--button"></div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'

const activeSection = ref('colors')

// Color data
const blueColors = [
  { level: '9', name: '最深蓝', value: '#2A2E7A' },
  { level: '8', name: '很深蓝', value: '#363699' },
  { level: '7', name: '主蓝色', value: '#434DBF' },
  { level: '6', name: '中蓝色', value: '#5761D9' },
  { level: '5', name: '浅蓝色', value: '#8594F2' },
  { level: '4', name: '更浅蓝', value: '#A9AFF1' },
  { level: '3', name: '很浅蓝', value: '#C4C9F7' },
  { level: '2', name: '超浅蓝', value: '#E6EAFF' },
  { level: '1', name: '最浅蓝', value: '#F7F9FF' },
]

const grayColors = [
  { level: '8', name: '主文本', value: '#090A1A' },
  { level: '7', name: '次文本', value: '#656870' },
  { level: '6', name: '三级文本', value: '#A3ACBF' },
  { level: '5', name: '浅灰文本', value: '#B8C2D9' },
  { level: '4', name: '边框', value: '#D9DCE6' },
  { level: '3', name: '分割线', value: '#E7EBF2' },
  { level: '2', name: '禁用背景', value: '#F7F8FC' },
  { level: '1', name: '白色', value: '#FFFFFF' },
]

const fontSizes = [
  { name: '2xs', label: '微小', value: '10px', sample: '微小文本' },
  { name: 'xs', label: '小号', value: '12px', sample: '小号文本' },
  { name: 'sm', label: '小型', value: '13px', sample: '小型文本' },
  { name: 'base', label: '基础', value: '14px', sample: '基础文本' },
  { name: 'md', label: '中等', value: '15px', sample: '中等文本' },
  { name: 'lg', label: '大型', value: '16px', sample: '大型文本' },
  { name: 'xl', label: '超大', value: '18px', sample: '超大文本' },
  { name: '2xl', label: '2倍大', value: '20px', sample: '2倍大文本' },
  { name: '3xl', label: '3倍大', value: '24px', sample: '3倍大文本' },
]

const spacingScale = [
  { name: '1', label: '1单位', value: '4px' },
  { name: '2', label: '2单位', value: '8px' },
  { name: '3', label: '3单位', value: '12px' },
  { name: '4', label: '4单位', value: '16px' },
  { name: '5', label: '5单位', value: '20px' },
  { name: '6', label: '6单位', value: '24px' },
  { name: '8', label: '8单位', value: '32px' },
  { name: '10', label: '10单位', value: '40px' },
  { name: '12', label: '12单位', value: '48px' },
  { name: '16', label: '16单位', value: '64px' },
]

const radiusScale = [
  { name: 'sm', label: '小圆角', value: '2px' },
  { name: 'base', label: '基础圆角', value: '4px' },
  { name: 'md', label: '中等圆角', value: '6px' },
  { name: 'lg', label: '大圆角', value: '8px' },
  { name: 'xl', label: '超大圆角', value: '12px' },
  { name: '2xl', label: '2倍圆角', value: '16px' },
  { name: 'full', label: '完全圆角', value: '9999px' },
]

const scrollToSection = (index: string) => {
  activeSection.value = index
  const element = document.getElementById(index)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

onMounted(() => {
  // Add scroll listener to update active section
  const handleScroll = () => {
    const sections = ['colors', 'typography', 'spacing', 'components', 'gradients', 'status']
    const scrollPosition = window.scrollY + 100

    for (const section of sections) {
      const element = document.getElementById(section)
      if (element) {
        const offsetTop = element.offsetTop
        const offsetBottom = offsetTop + element.offsetHeight

        if (scrollPosition >= offsetTop && scrollPosition < offsetBottom) {
          activeSection.value = section
          break
        }
      }
    }
  }

  window.addEventListener('scroll', handleScroll)

  return () => {
    window.removeEventListener('scroll', handleScroll)
  }
})
</script>

<style scoped>
.design-system {
  min-height: 100vh;
  background-color: var(--color-bg);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

/* Header */
.ds-header {
  background: var(--gradient-galaxy-blue);
  color: var(--color-by1);
  padding: var(--space-16) 0;
  text-align: center;
}

.ds-header h1 {
  font-size: var(--font-size-6xl);
  font-weight: var(--font-weight-bold);
  margin: 0 0 var(--space-4) 0;
  background: var(--color-by1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.ds-subtitle {
  font-size: var(--font-size-xl);
  opacity: 0.9;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
}

/* Navigation */
.ds-nav {
  background-color: var(--color-by1);
  border-bottom: 1px solid var(--color-by3);
  position: sticky;
  top: 0;
  z-index: 100;
}

.ds-menu {
  border-bottom: none;
}

/* Content */
.ds-content {
  padding: var(--space-12) 0;
}

.ds-section {
  margin-bottom: var(--space-20);
  scroll-margin-top: 80px;
}

.ds-section h2 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-by8);
  margin: 0 0 var(--space-4) 0;
}

.section-description {
  font-size: var(--font-size-lg);
  color: var(--color-by7);
  margin: 0 0 var(--space-8) 0;
  line-height: 1.6;
}

/* Color System */
.color-group {
  margin-bottom: var(--space-12);
}

.color-group h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-by8);
  margin: 0 0 var(--space-6) 0;
}

.color-palette {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.color-item {
  background-color: var(--color-by1);
  border: 1px solid var(--color-by3);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
}

.color-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.color-swatch {
  height: 80px;
  width: 100%;
}

.color-info {
  padding: var(--space-4);
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.color-name {
  font-weight: var(--font-weight-semibold);
  color: var(--color-by8);
}

.color-value {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  color: var(--color-by7);
}

.color-var {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-xs);
  color: var(--color-by6);
  background-color: var(--color-by2);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-base);
}

/* Typography */
.typography-group {
  margin-bottom: var(--space-12);
}

.typography-group h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-by8);
  margin: 0 0 var(--space-6) 0;
}

.font-families {
  display: grid;
  gap: var(--space-6);
}

.font-family-item {
  background-color: var(--color-by1);
  border: 1px solid var(--color-by3);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.font-sample {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--space-3);
  color: var(--color-by8);
}

.font-name {
  display: block;
  font-weight: var(--font-weight-semibold);
  color: var(--color-by8);
  margin-bottom: var(--space-1);
}

.font-var {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  color: var(--color-by6);
  background-color: var(--color-by2);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-base);
}

.font-sizes {
  display: grid;
  gap: var(--space-4);
}

.font-size-item {
  background-color: var(--color-by1);
  border: 1px solid var(--color-by3);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.font-size-item .font-sample {
  margin: 0;
  color: var(--color-by8);
}

.font-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--space-1);
}

.font-value {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  color: var(--color-by7);
}

.headings {
  background-color: var(--color-by1);
  border: 1px solid var(--color-by3);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-sm);
}

.headings h1,
.headings h2,
.headings h3,
.headings h4,
.headings h5,
.headings h6 {
  margin: 0 0 var(--space-4) 0;
  color: var(--color-by8);
}

.headings h6 {
  margin-bottom: 0;
}

/* Spacing */
.spacing-group {
  margin-bottom: var(--space-12);
}

.spacing-group h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-by8);
  margin: 0 0 var(--space-6) 0;
}

.spacing-scale {
  display: grid;
  gap: var(--space-3);
}

.spacing-item {
  background-color: var(--color-by1);
  border: 1px solid var(--color-by3);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  box-shadow: var(--shadow-sm);
}

.spacing-visual {
  flex: 1;
  height: 20px;
  background-color: var(--color-by2);
  border-radius: var(--radius-base);
  position: relative;
}

.spacing-bar {
  height: 100%;
  background-color: var(--color-blue-main);
  border-radius: var(--radius-base);
  min-width: 4px;
}

.spacing-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--space-1);
  min-width: 120px;
}

.spacing-name {
  font-weight: var(--font-weight-semibold);
  color: var(--color-by8);
}

.spacing-value {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  color: var(--color-by7);
}

.spacing-var {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-xs);
  color: var(--color-by6);
  background-color: var(--color-by2);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-base);
}

.radius-scale {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.radius-item {
  background-color: var(--color-by1);
  border: 1px solid var(--color-by3);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  text-align: center;
}

.radius-visual {
  width: 60px;
  height: 60px;
  background-color: var(--color-blue-main);
  margin: 0 auto var(--space-4) auto;
}

.radius-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.radius-name {
  font-weight: var(--font-weight-semibold);
  color: var(--color-by8);
}

.radius-value {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  color: var(--color-by7);
}

.radius-var {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-xs);
  color: var(--color-by6);
  background-color: var(--color-by2);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-base);
}

/* Components */
.component-group {
  margin-bottom: var(--space-12);
}

.component-group h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-by8);
  margin: 0 0 var(--space-6) 0;
}

.component-demo {
  background-color: var(--color-by1);
  border: 1px solid var(--color-by3);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-sm);
}

.demo-row {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.demo-row:last-child {
  margin-bottom: 0;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

.demo-card {
  margin: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-4);
  }

  .ds-header {
    padding: var(--space-12) 0;
  }

  .ds-header h1 {
    font-size: var(--font-size-4xl);
  }

  .ds-subtitle {
    font-size: var(--font-size-lg);
  }

  .color-palette {
    grid-template-columns: 1fr;
  }

  .font-size-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }

  .font-info {
    align-items: flex-start;
  }

  .spacing-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }

  .spacing-info {
    align-items: flex-start;
  }

  .radius-scale {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .card-grid {
    grid-template-columns: 1fr;
  }

  .demo-row {
    flex-direction: column;
  }
}

/* Gradients */
.gradient-group {
  margin-bottom: var(--space-12);
}

.gradient-group h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-by8);
  margin: 0 0 var(--space-6) 0;
}

.gradient-palette {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.gradient-item {
  background-color: var(--color-by1);
  border: 1px solid var(--color-by3);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
}

.gradient-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.gradient-swatch {
  height: 80px;
  width: 100%;
}

.gradient-info {
  padding: var(--space-4);
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.gradient-name {
  font-weight: var(--font-weight-semibold);
  color: var(--color-by8);
}

.gradient-var {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-xs);
  color: var(--color-by6);
  background-color: var(--color-by2);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-base);
}

.gradient-text-demo {
  background-color: var(--color-by1);
  border: 1px solid var(--color-by3);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
  align-items: center;
}

.gradient-text-demo .gradient-text {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  text-align: center;
}

.gradient-cards-demo {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

.gradient-cards-demo .gradient-card h4 {
  margin: 0 0 var(--space-3) 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

.gradient-cards-demo .gradient-card p {
  margin: 0;
  opacity: 0.9;
}

/* Status Components */
.status-group {
  margin-bottom: var(--space-12);
}

.status-group h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-by8);
  margin: 0 0 var(--space-6) 0;
}

.status-demo {
  background-color: var(--color-by1);
  border: 1px solid var(--color-by3);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-sm);
}

.status-row {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.status-row:last-child {
  margin-bottom: 0;
}

.loading-demo {
  background-color: var(--color-by1);
  border: 1px solid var(--color-by3);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-sm);
}

.loading-row {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
  align-items: flex-start;
}

.loading-row:last-child {
  margin-bottom: 0;
}

.animation-demo {
  background-color: var(--color-by1);
  border: 1px solid var(--color-by3);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-sm);
}

.animation-row {
  display: flex;
  gap: var(--space-8);
  align-items: center;
  justify-content: center;
}

/* Responsive for new sections */
@media (max-width: 768px) {
  .gradient-palette {
    grid-template-columns: 1fr;
  }

  .gradient-text-demo .gradient-text {
    font-size: var(--font-size-2xl);
  }

  .gradient-cards-demo {
    grid-template-columns: 1fr;
  }

  .status-row {
    flex-direction: column;
  }

  .loading-row {
    flex-direction: column;
  }

  .animation-row {
    flex-direction: column;
    gap: var(--space-4);
  }
}
</style>
