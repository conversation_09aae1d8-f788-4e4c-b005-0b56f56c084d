<template>
  <div class="banner" :class="gradientClass">
    <div class="banner__content">
      <h1 class="banner__title" :class="{ 'gradient-text': textGradient, ['gradient-text--' + textGradientType]: textGradient }">{{ title }}</h1>
      <p class="banner__description">{{ description }}</p>
      <div class="banner__actions" v-if="showActions">
        <el-button type="primary" size="large" @click="$emit('action-primary')">
          {{ primaryButtonText }}
        </el-button>
        <el-button size="large" @click="$emit('action-secondary')">
          {{ secondaryButtonText }}
        </el-button>
      </div>
    </div>
    <div class="banner__image" v-if="imageUrl">
      <img :src="imageUrl" :alt="title" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  title: {
    type: String,
    default: '欢迎使用 Vue3 + Element Plus'
  },
  description: {
    type: String,
    default: '基于 Vue 3 和 Element Plus 的现代化前端项目模板'
  },
  imageUrl: {
    type: String,
    default: ''
  },
  showActions: {
    type: Boolean,
    default: true
  },
  primaryButtonText: {
    type: String,
    default: '开始使用'
  },
  secondaryButtonText: {
    type: String,
    default: '了解更多'
  },
  gradient: {
    type: String,
    default: 'galaxy-blue', // 默认使用 galaxy-blue 渐变
    validator: (value) => [
      '', 'galaxy-blue', 'galaxy-red', 'blue-deep', 'red-deep', 
      'green-deep', 'orange-deep', 'purple-deep', 'teal-deep',
      'blue-light', 'red-light', 'green-light', 'orange-light', 
      'purple-light', 'teal-light', 'gold-light', 'rainbow-warm',
      'rainbow-cool', 'sunset', 'ocean', 'forest'
    ].includes(value)
  },
  textGradient: {
    type: Boolean,
    default: false
  },
  textGradientType: {
    type: String,
    default: 'galaxy-blue'
  }
})

const gradientClass = computed(() => {
  return props.gradient ? `gradient-bg--${props.gradient}` : '';
});

defineEmits(['action-primary', 'action-secondary'])
</script>

<style lang="scss" scoped>
@use '@/styles/abstracts/colors' as *;
@use '@/styles/abstracts/mixins' as *;

.banner {
  display: flex;
  align-items: center;
  gap: 48px;
  padding: 60px 0;
  min-height: 400px;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  
  @include respond-to('tablet') {
    flex-direction: column;
    text-align: center;
    gap: 32px;
    padding: 40px 0;
  }
  
  &__content {
    flex: 1;
    padding: 0 40px;
    position: relative;
    z-index: 2;
    
    @include respond-to('tablet') {
      padding: 0 20px;
    }
  }
  
  &__title {
    font-size: 48px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 16px;
    color: $text-color-primary;
    
    @include respond-to('tablet') {
      font-size: 36px;
    }
  }
  
  &__description {
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 32px;
    color: $text-color-regular;
    
    @include respond-to('tablet') {
      font-size: 16px;
      margin-bottom: 24px;
    }
  }
  
  &__actions {
    display: flex;
    gap: 16px;
    
    @include respond-to('mobile') {
      flex-direction: column;
    }
  }
  
  &__image {
    flex: 1;
    max-width: 500px;
    position: relative;
    z-index: 2;
    
    img {
      width: 100%;
      height: auto;
      border-radius: 8px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    }
  }
}

// 当使用渐变背景时，调整文字颜色以确保可读性
.gradient-bg--blue-deep,
.gradient-bg--red-deep,
.gradient-bg--green-deep,
.gradient-bg--orange-deep,
.gradient-bg--purple-deep,
.gradient-bg--teal-deep,
.gradient-bg--galaxy-red,
.gradient-bg--galaxy-blue,
.gradient-bg--royal-purple,
.gradient-bg--sunset,
.gradient-bg--ocean,
.gradient-bg--forest {
  .banner__title,
  .banner__description {
    color: #ffffff;
  }
}
</style>