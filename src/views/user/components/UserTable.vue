<template>
  <el-card>
    <el-table
      :data="data"
      :loading="loading"
      row-key="id"
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column prop="avatar" label="头像" width="80">
        <template #default="{ row }">
          <el-avatar :src="row.avatar" :alt="row.name">
            {{ row.name?.charAt(0) }}
          </el-avatar>
        </template>
      </el-table-column>
      
      <el-table-column prop="name" label="姓名" min-width="120" />
      
      <el-table-column prop="email" label="邮箱" min-width="180" />
      
      <el-table-column prop="phone" label="手机号" min-width="130" />
      
      <el-table-column prop="roles" label="角色" min-width="120">
        <template #default="{ row }">
          <el-tag
            v-for="role in row.roles"
            :key="role"
            size="small"
            :type="getRoleTagType(role)"
          >
            {{ getRoleText(role) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
            {{ row.status === 'active' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="lastLoginTime" label="最后登录" min-width="150">
        <template #default="{ row }">
          {{ formatTime(row.lastLoginTime) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="createTime" label="创建时间" min-width="150">
        <template #default="{ row }">
          {{ formatTime(row.createTime) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            text
            @click="$emit('edit', row)"
          >
            编辑
          </el-button>
          
          <el-button
            :type="row.status === 'active' ? 'warning' : 'success'"
            size="small"
            text
            @click="$emit('toggle-status', row)"
          >
            {{ row.status === 'active' ? '禁用' : '启用' }}
          </el-button>
          
          <el-button
            type="danger"
            size="small"
            text
            @click="$emit('delete', row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed } from 'vue'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  pagination: {
    type: Object,
    required: true
  }
})

const emit = defineEmits([
  'edit',
  'delete', 
  'toggle-status',
  'page-change',
  'size-change',
  'selection-change'
])

const selectedRows = ref([])

const currentPage = computed({
  get: () => props.pagination.current,
  set: (value) => emit('page-change', value)
})

// 方法
const getRoleTagType = (role) => {
  const typeMap = {
    admin: 'danger',
    manager: 'warning',
    user: 'info'
  }
  return typeMap[role] || 'info'
}

const getRoleText = (role) => {
  const textMap = {
    admin: '管理员',
    manager: '经理',
    user: '普通用户'
  }
  return textMap[role] || role
}

const formatTime = (time) => {
  if (!time) return '-'
  
  try {
    return formatDistanceToNow(new Date(time), {
      addSuffix: true,
      locale: zhCN
    })
  } catch {
    return time
  }
}

const handleSelectionChange = (selection) => {
  selectedRows.value = selection
  emit('selection-change', selection)
}

const handleSizeChange = (size) => {
  emit('size-change', size)
}

const handleCurrentChange = (page) => {
  emit('page-change', page)
}
</script>

<style lang="scss" scoped>
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.el-tag + .el-tag {
  margin-left: 4px;
}
</style>
