<template>
  <div class="product-page">
    <PageHeader
      title="产品管理"
      description="管理产品信息，包括产品分类、库存、价格等"
    >
      <template #actions>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
        <el-button type="primary" @click="handleCreateProduct">
          <el-icon><Plus /></el-icon>
          新增产品
        </el-button>
      </template>
    </PageHeader>
    
    <!-- 产品统计卡片 -->
    <ProductStatistics :statistics="statistics" />
    
    <!-- 搜索和筛选 -->
    <ProductSearchForm 
      v-model:filters="searchFilters"
      :categories="categories"
      @search="handleSearch"
      @reset="handleReset"
    />
    
    <!-- 产品列表 -->
    <ProductGrid
      v-if="viewMode === 'grid'"
      :products="products"
      :loading="loading"
      @edit="handleEditProduct"
      @delete="handleDeleteProduct"
      @view="handleViewProduct"
    />
    
    <ProductTable
      v-else
      :data="products"
      :loading="loading"
      :pagination="pagination"
      @edit="handleEditProduct"
      @delete="handleDeleteProduct"
      @page-change="handlePageChange"
    />
    
    <!-- 视图切换和分页 -->
    <div class="view-controls">
      <div class="view-mode">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button value="grid">
            <el-icon><Grid /></el-icon>
            网格
          </el-radio-button>
          <el-radio-button value="table">
            <el-icon><List /></el-icon>
            列表
          </el-radio-button>
        </el-radio-group>
      </div>
      
      <el-pagination
        v-if="viewMode === 'grid'"
        v-model:current-page="pagination.current"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        layout="prev, pager, next"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Download, Grid, List } from '@element-plus/icons-vue'
import { useStore } from 'vuex'
import PageHeader from '@/components/common/PageHeader/index.vue'
import ProductStatistics from './components/ProductStatistics.vue'
import ProductSearchForm from './components/ProductSearchForm.vue'
import ProductGrid from './components/ProductGrid.vue'
import ProductTable from './components/ProductTable.vue'

const store = useStore()

// 响应式数据
const viewMode = ref('grid')
const searchFilters = reactive({
  keyword: '',
  category: '',
  status: '',
  priceRange: null
})

// 计算属性
const products = computed(() => store.getters['product/productList'])
const loading = computed(() => store.state.product.loading.list)
const pagination = computed(() => store.state.product.pagination)
const statistics = computed(() => store.state.product.statistics)
const categories = computed(() => store.state.product.categories)

// 方法
const fetchData = async () => {
  try {
    await Promise.all([
      store.dispatch('product/fetchProducts'),
      store.dispatch('product/fetchCategories'),
      store.dispatch('product/fetchStatistics')
    ])
  } catch (error) {
    ElMessage.error('获取数据失败')
  }
}

const handleSearch = () => {
  store.dispatch('product/setFilters', searchFilters)
}

const handleReset = () => {
  Object.assign(searchFilters, {
    keyword: '',
    category: '',
    status: '',
    priceRange: null
  })
  handleSearch()
}

const handleCreateProduct = () => {
  // 跳转到产品创建页面
  // router.push('/product/create')
  ElMessage.info('跳转到产品创建页面')
}

const handleEditProduct = (product) => {
  // 跳转到产品编辑页面
  // router.push(`/product/edit/${product.id}`)
  ElMessage.info(`编辑产品: ${product.name}`)
}

const handleViewProduct = (product) => {
  // 跳转到产品详情页面
  // router.push(`/product/${product.id}`)
  ElMessage.info(`查看产品: ${product.name}`)
}

const handleDeleteProduct = async (product) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除产品 "${product.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await store.dispatch('product/deleteProduct', product.id)
    ElMessage.success('删除成功')
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handlePageChange = (page) => {
  store.dispatch('product/fetchProducts', { page })
}

const handleExport = () => {
  ElMessage.info('导出功能开发中')
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
@use '@/styles/abstracts/colors' as *;

.product-page {
  padding: 24px;
  
  .view-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 16px;
    background: $bg-color-base;
    border-radius: 8px;
  }
  
  .view-mode {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}
</style>
