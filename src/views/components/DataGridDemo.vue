<template>
  <div class="data-grid-demo">
    <div class="page-header">
      <h1>DataGrid 组件演示</h1>
      <p>高级数据表格组件，支持配置化列生成、数据源抽象、操作列统一、自定义列渲染、高级筛选等功能</p>
    </div>

    <!-- 基础表格示例 -->
    <div class="demo-section">
      <h2>基础表格</h2>
      <p>展示基本的表格功能，支持分页、排序、筛选</p>
      <DataGrid :config="basicConfig" />
    </div>

    <!-- 带操作列的表格 -->
    <div class="demo-section">
      <h2>带操作列的表格</h2>
      <p>包含编辑、删除、详情等操作按钮，支持按钮状态控制</p>
      <DataGrid :config="actionConfig" />
    </div>

    <!-- 带高级筛选的表格 -->
    <div class="demo-section">
      <h2>带高级筛选的表格</h2>
      <p>集成高级筛选功能，支持多条件筛选和表单联动</p>
      <DataGrid :config="filterConfig" />
    </div>

    <!-- 远程数据表格 -->
    <div class="demo-section">
      <h2>远程数据表格</h2>
      <p>从远程 API 获取数据，支持服务端分页和排序</p>
      <DataGrid :config="remoteConfig" />
    </div>
  </div>
</template>

<script setup lang="ts">
import DataGrid from '@/components/common/DataGrid.vue'
import type { DataGridConfig } from '@/types/data-grid'
import { ElMessage } from 'element-plus'

// 基础表格配置
const basicConfig: DataGridConfig = {
  columns: [
    {
      prop: 'id',
      label: 'ID',
      width: 80,
      sortable: true
    },
    {
      prop: 'name',
      label: '姓名',
      minWidth: 120,
      sortable: true,
      showOverflowTooltip: true
    },
    {
      prop: 'email',
      label: '邮箱',
      minWidth: 180,
      showOverflowTooltip: true
    },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      type: 'boolean',
      filterable: true,
      filterOptions: [
        { text: '活跃', value: true },
        { text: '非活跃', value: false }
      ]
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 180,
      type: 'date',
      sortable: true
    }
  ],
  dataSource: {
    data: [
      { id: 1, name: '张三', email: '<EMAIL>', status: true, createTime: '2024-01-01 10:00:00' },
      { id: 2, name: '李四', email: '<EMAIL>', status: false, createTime: '2024-01-02 11:00:00' },
      { id: 3, name: '王五', email: '<EMAIL>', status: true, createTime: '2024-01-03 12:00:00' },
      { id: 4, name: '赵六', email: '<EMAIL>', status: true, createTime: '2024-01-04 13:00:00' },
      { id: 5, name: '孙七', email: '<EMAIL>', status: false, createTime: '2024-01-05 14:00:00' },
      { id: 6, name: '周八', email: '<EMAIL>', status: true, createTime: '2024-01-06 15:00:00' },
      { id: 7, name: '吴九', email: '<EMAIL>', status: false, createTime: '2024-01-07 16:00:00' },
      { id: 8, name: '郑十', email: '<EMAIL>', status: true, createTime: '2024-01-08 17:00:00' }
    ],
    pagination: {
      enabled: true,
      pageSize: 3
    }
  },
  tableProps: {
    stripe: true,
    border: true,
    size: 'default'
  }
}

// 带操作列的表格配置
const actionConfig: DataGridConfig = {
  ...basicConfig,
  actionColumn: {
    label: '操作',
    width: 200,
    fixed: 'right',
    actions: [
      {
        text: '编辑',
        type: 'primary',
        size: 'small',
        onClick: (row, index) => {
          ElMessage.success(`编辑用户：${row.name}`)
        }
      },
      {
        text: '删除',
        type: 'danger',
        size: 'small',
        disabled: (row) => row.status === false,
        onClick: (row, index) => {
          ElMessage.warning(`删除用户：${row.name}`)
        }
      },
      {
        text: '详情',
        type: 'text',
        size: 'small',
        onClick: (row, index) => {
          ElMessage.info(`查看用户详情：${row.name}`)
        }
      }
    ]
  }
}

// 带高级筛选的表格配置
const filterConfig: DataGridConfig = {
  ...actionConfig,
  advancedFilter: {
    enabled: true,
    collapsible: true,
    defaultCollapsed: false,
    layout: 'inline',
    fields: [
      {
        prop: 'name',
        label: '姓名',
        type: 'input',
        placeholder: '请输入姓名',
        span: 6
      },
      {
        prop: 'email',
        label: '邮箱',
        type: 'input',
        placeholder: '请输入邮箱',
        span: 6
      },
      {
        prop: 'status',
        label: '状态',
        type: 'select',
        placeholder: '请选择状态',
        span: 6,
        options: [
          { label: '活跃', value: true },
          { label: '非活跃', value: false }
        ]
      },
      {
        prop: 'createTime',
        label: '创建时间',
        type: 'daterange',
        span: 6
      }
    ]
  }
}

// 远程数据表格配置
const remoteConfig: DataGridConfig = {
  columns: [
    {
      prop: 'id',
      label: 'ID',
      width: 80
    },
    {
      prop: 'title',
      label: '标题',
      minWidth: 200,
      showOverflowTooltip: true
    },
    {
      prop: 'body',
      label: '内容',
      minWidth: 300,
      showOverflowTooltip: true
    },
    {
      prop: 'userId',
      label: '用户ID',
      width: 100
    }
  ],
  dataSource: {
    remote: {
      url: 'https://jsonplaceholder.typicode.com/posts',
      method: 'GET',
      transformResponse: (response) => ({
        data: response.slice(0, 20), // 只取前20条数据
        total: 20,
        page: 1,
        pageSize: 20
      })
    },
    pagination: {
      enabled: true,
      pageSize: 5
    }
  }
}
</script>

<style scoped>
.data-grid-demo {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 24px;
  margin-bottom: 10px;
}

.demo-section {
  margin-bottom: 40px;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.demo-section h2 {
  font-size: 18px;
  margin-bottom: 15px;
}

.demo-section p {
  margin-bottom: 20px;
  color: #666;
}
</style>
