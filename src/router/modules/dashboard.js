import ContentLayout from '@/layouts/ContentLayout.vue'

const dashboardRoutes = [
  {
    path: '/dashboard',
    component: ContentLayout,
    redirect: '/dashboard/index',
    meta: {
      title: '数据看板',
      icon: 'DataAnalysis'
    },
    children: [
      {
        path: 'index',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: {
          title: '数据看板',
          icon: 'DataAnalysis',
          keepAlive: true
        }
      }
    ]
  }
]

export default dashboardRoutes