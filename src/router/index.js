import { createRouter, createWebHistory } from 'vue-router'
import * as VueRouter from 'vue-router'
import homeRoutes from './modules/home'
import aboutRoutes from './modules/about'
import dashboardRoutes from './modules/dashboard'
import componentsRoutes from './modules/components'

const routes = [
  ...homeRoutes,
  ...aboutRoutes,
  ...dashboardRoutes,
  ...componentsRoutes
]

const router = createRouter({
  // history: createWebHistory(import.meta.env.BASE_URL),
  history: VueRouter.createWebHashHistory(),
  routes
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 权限验证
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next({ name: 'login', query: { redirect: to.fullPath } })
  } else if (to.meta.permissions && !hasPermission(to.meta.permissions)) {
    next({ name: 'unauthorized' })
  } else {
    next()
  }
})

// 全局后置钩子
router.afterEach((to) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - Vue3 Element Plus` : 'Vue3 Element Plus'
})

// 辅助函数（示例）
function isAuthenticated() {
  return !!localStorage.getItem('token')
}

function hasPermission(permissions) {
  // 权限检查逻辑
  return true
}

export default router
