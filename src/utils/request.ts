import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'

// 请求接口类型
export interface RequestConfig extends AxiosRequestConfig {
  showLoading?: boolean
  showError?: boolean
  timeout?: number
}

// 响应数据类型
export interface ResponseData<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 创建 axios 实例
const instance: AxiosInstance = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 添加认证 token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
instance.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data } = response
    
    // 直接返回外部 API 的数据（如 jsonplaceholder）
    if (response.config.url?.includes('jsonplaceholder.typicode.com')) {
      return response.data
    }
    
    // 处理内部 API 响应
    if (data.code === 200 || data.success) {
      return data.data || data
    }
    
    // 处理业务错误
    const error = new Error(data.message || '请求失败')
    return Promise.reject(error)
  },
  (error) => {
    // 处理 HTTP 错误
    let message = '请求失败'
    
    if (error.response) {
      const { status, data } = error.response
      switch (status) {
        case 401:
          message = '未授权，请重新登录'
          // 可以在这里处理登录跳转
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = data?.message || `请求失败 (${status})`
      }
    } else if (error.request) {
      message = '网络连接失败'
    } else {
      message = error.message || '请求配置错误'
    }
    
    console.error('Request Error:', message, error)
    return Promise.reject(new Error(message))
  }
)

// 通用请求方法
export function request<T = any>(config: RequestConfig): Promise<T> {
  return instance.request<T>(config)
}

// GET 请求
export function get<T = any>(url: string, params?: any, config?: RequestConfig): Promise<T> {
  return request<T>({
    url,
    method: 'GET',
    params,
    ...config
  })
}

// POST 请求
export function post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
  return request<T>({
    url,
    method: 'POST',
    data,
    ...config
  })
}

// PUT 请求
export function put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
  return request<T>({
    url,
    method: 'PUT',
    data,
    ...config
  })
}

// DELETE 请求
export function del<T = any>(url: string, config?: RequestConfig): Promise<T> {
  return request<T>({
    url,
    method: 'DELETE',
    ...config
  })
}

// 文件上传
export function upload<T = any>(url: string, file: File, config?: RequestConfig): Promise<T> {
  const formData = new FormData()
  formData.append('file', file)
  
  return request<T>({
    url,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    ...config
  })
}

// 并发请求
export function all<T = any>(requests: Array<Promise<T>>): Promise<T[]> {
  return Promise.all(requests)
}

export default instance
