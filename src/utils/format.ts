// Use a simple date formatter instead of dayjs to avoid compatibility issues
function formatDateString(date: any, format = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return String(date);
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

/**
 * 格式化日期
 */
export function formatDate(value: any, format = 'YYYY-MM-DD HH:mm:ss'): string {
  return formatDateString(value, format)
}

/**
 * 格式化数字
 */
export function formatNumber(value: any, precision = 2): string {
  if (value === null || value === undefined || value === '') return ''
  const num = Number(value)
  if (isNaN(num)) return String(value)
  return num.toFixed(precision)
}

/**
 * 格式化布尔值
 */
export function formatBoolean(value: any, trueText = '是', falseText = '否'): string {
  if (value === null || value === undefined || value === '') return ''
  return Boolean(value) ? trueText : falseText
}

/**
 * 格式化文件大小
 */
export function formatFileSize(size: number): string {
  if (!size) return '0 B'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let index = 0
  let value = size
  
  while (value >= 1024 && index < units.length - 1) {
    value /= 1024
    index++
  }
  
  return `${value.toFixed(1)} ${units[index]}`
}

/**
 * 格式化货币
 */
export function formatCurrency(value: any, currency = '¥', precision = 2): string {
  if (value === null || value === undefined || value === '') return ''
  const num = Number(value)
  if (isNaN(num)) return String(value)
  return `${currency}${num.toFixed(precision)}`
}

/**
 * 格式化百分比
 */
export function formatPercent(value: any, precision = 2): string {
  if (value === null || value === undefined || value === '') return ''
  const num = Number(value)
  if (isNaN(num)) return String(value)
  return `${(num * 100).toFixed(precision)}%`
}

/**
 * 格式化枚举值
 */
export function formatEnum(value: any, enumMap: Record<string, string>): string {
  if (value === null || value === undefined || value === '') return ''
  return enumMap[value] || String(value)
}

/**
 * 脱敏处理
 */
export function formatSensitive(value: string, type: 'phone' | 'email' | 'idcard' | 'bankcard' = 'phone'): string {
  if (!value) return ''
  
  switch (type) {
    case 'phone':
      return value.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    case 'email':
      return value.replace(/(.{2}).*(@.*)/, '$1****$2')
    case 'idcard':
      return value.replace(/(\d{4})\d{10}(\d{4})/, '$1**********$2')
    case 'bankcard':
      return value.replace(/(\d{4})\d*(\d{4})/, '$1****$2')
    default:
      return value
  }
}
