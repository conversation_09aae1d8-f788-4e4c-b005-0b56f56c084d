import axios from 'axios'
// import userApi from './modules/user'
// import productApi from './modules/product'

// 创建 axios 实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 添加 token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => Promise.reject(error)
)

// 响应拦截器
request.interceptors.response.use(
  response => response.data,
  error => {
    // 统一错误处理
    if (error.response) {
      // 401 未授权
      if (error.response.status === 401) {
        // 跳转到登录页
        console.warn('Token expired, redirect to login')
      }
    }
    return Promise.reject(error)
  }
)

export default {
  // user: userApi(request),
  // product: productApi(request)
}
