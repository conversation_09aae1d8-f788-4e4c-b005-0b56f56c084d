Vue 代码工程示例结构：

## 1. 项目结构规范

```
vue-project/
├── public/                     # 静态资源
│   ├── index.html
│   └── favicon.ico
├── src/
│   ├── api/                    # API 接口管理
│   │   ├── modules/
│   │   │   ├── user.js
│   │   │   └── product.js
│   │   └── index.js
│   ├── assets/                 # 会被引用的静态资源，如 `import '@/assets/images/logo.png'`
│   │   ├── images/
│   │   ├── icons/
│   │   └── fonts/
│   ├── components/             # 通用组件
│   │   ├── ui/                 # 纯 UI 组件，如按钮、输入框、表格等，剥离业务概念
│   │   │   ├── BaseButton/
│   │   │   │   ├── index.vue
│   │   │   │   └── index.scss
│   │   │   └── BaseInput/
│   │   └── business/           # 业务组件，不建议这里放任何组件，优先放在 `/views/.../components` 下
│   │       ├── UserCard/
│   │       └── ProductList/
│   ├── composables/            # 组合式函数，更建议放在 `/views/.../composables` 下
│   ├── context/                # 上下文封装
│   │   ├── api/                # 客户端 API
│   │   │   ├── index.js        # 导出 api 实例
│   │   │   └── context.js      # 提供 provideApi() 和 injectApi(), useApi() 方法
│   │   ├── config/             # 配置，如主题、语言等，如：`useConfig`
│   │   │   ├── index.js
│   │   │   └── context.js      # 提供 provideConfig() 和 injectConfig(), useConfig() 方法
│   │   ├── auth/               # 上下文，如用户信息、权限等，如：`useAuth`
│   │   │   ├── index.js
│   │   │   └── context.js      # 提供 provideAuth() 和 injectAuth(), useAuth() 方法
│   │   ├── business/           # 专门用于业务上下文，如：`useProduct`, `useWallet`
│   │   │   ├── product/
│   │   │   │   ├── index.js
│   │   │   │   └── context.js      # 提供 provideProduct() 和 injectProduct(), useProduct() 方法
│   │   │   └── wallet/
│   │   │       ├── index.js
│   │   │       └── context.js      # 提供 provideWallet() 和 injectWallet(), useWallet() 方法
│   ├── directives/             # 自定义指令, 不建议自己封装指令，优先使用 Vue 和组件库自带的指令
│   ├── layouts/                # 布局组件，有点多余，放在 `/views/.../index/components` 或 `/components/layouts` 即可
│   │   ├── DefaultLayout.vue
│   │   └── AuthLayout.vue
│   ├── views/                  # 页面组件
│   │   ├── home/
│   │   │   ├── index.vue
│   │   │   ├── composables/    # 页面组件的组合式函数，如：`views/home/<USER>/useHome.js`
│   │   │   └── components/     # 页面组件的子组件，如：`views/home/<USER>/Header.vue`
│   │   └── user/
│   ├── router/                 # 路由配置, 最好不拆分 modules，直接在 index.js 中配所有的，除非页面达到一定数量再拆分
│   │   ├── index.js
│   │   └── modules/
│   ├── store/                  # 状态管理，不建议使用全局状态管理，优先使用组件内状态或 composable 或 context 管理
│   │   ├── index.js
│   │   └── modules/
│   │       ├── user.js
│   │       └── app.js
│   ├── styles/                 # 样式文件
│   │   ├── abstracts/          # SCSS 抽象层
│   │   │   ├── _variables.scss
│   │   │   ├── _mixins.scss
│   │   │   └── _functions.scss
│   │   ├── base/               # 基础样式
│   │   │   ├── _reset.scss
│   │   │   └── _typography.scss
│   │   ├── components/         # 组件样式, 不建议，样式跟着组件走就行
│   │   ├── layouts/            # 布局样式，不建议，布局跟着组件走就行
│   │   ├── pages/              # 页面样式，不建议，页面样式跟着页面走就行
│   │   └── main.scss           # 主样式入口
│   ├── utils/                  # 工具函数，但容易成为代码的泥潭，不建议使用
│   │   ├── request.js          # 建议放在 /api/impl/request.js
│   │   ├── storage.js          # 建议放在 /lib/storage.js
│   │   └── validators.js       # 建议 inline 在组件中，不要单独抽出来。或者 composable/useValidators.js
│   ├── App.vue
│   └── main.js
├── tests/                      # 测试文件
├── .env                        # 环境变量
├── .env.development
├── .env.production
├── .eslintrc.js
├── .gitignore
├── package.json
└── vue.config.js / vite.config.js
```

CSS 规范：
- 使用 SCSS 作为预处理器（`sass`/`sass-embedded`）
- 有业务就业务出色系，没有业务的中后台应用以模板应用中的 `_galaxy_color.scss` 为主
- 引入设计系统变量（`src/styles/_variables.scss`）（如颜色(`_colors.scss`)、字号等）集中管理
- 建议使用银河自定义色系替换 Element Plus（`src/styles/element-plus-variable.scss`），参考 [自定义主题](https://element-plus.org/zh-CN/guide/theming.html)
- 样式结构建议使用 ITCSS + BEM 命名法等；
- 其它

组件：
- 组件命名：PascalCase，文件名与组件名保持一致
- 单文件组件结构顺序（SFC）
- 组件编码规范：使用组合式 API（Vue 3）
- 其它

路由规范：
- 所有页面定义在 `views/` 下，每个路由对应一个页面组件
- 使用 `Vue Router` 的懒加载（`defineAsyncComponent` 或 `() => import()`）
- 路由名称使用 kebab-case，如：xxx

状态管理规范:
- 不建议使用全局状态管理，优先使用组件内状态或 composable 或 context 管理
- Vue 3 项目可以使用 Pinia（推荐）或 Vuex
- 对于存在 Vue 2 要迁移的 Vue 3，优先建议使用 Vuex

技术选型：
- 对于不需要迁移可以考虑使用 Vite （Vue 官方推荐）
- 建议有 Vue 2 的项目使用 Vue CLI 5 + ESBuild，方便后续项目迁移；
- 组件库建议：对于 Vue 3 项目优先考虑使用 Element plus，特别是有存量的 Vue 2 + Element UI 可以方便未来迁移

ESLint： 
 - JavaScript: `eslint-plugin-vue`
 - TypeScript
   - npm install `@vue/eslint-config-typescript` @types/eslint @vue/eslint-config-prettier

注释与文档：
- 所有公共组件（即：`src/components`）、工具函数（`src/utils`）、API 模块应添加注释
- 使用 JSDoc/TSdoc 格式规范：

包体积页面与构建速度 ？？迁移微前端，拆分成两个应用？
