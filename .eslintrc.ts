import { defineConfigWithVueTs, vueTsConfigs } from '@vue/eslint-config-typescript'
import pluginVue from 'eslint-plugin-vue'
import pluginVitest from '@vitest/eslint-plugin'
import skipFormatting from '@vue/eslint-config-prettier/skip-formatting'

// 取消下面三行注释，以允许在 .vue 文件中使用更多的
// 语言
import { configureVueProject } from '@vue/eslint-config-typescript'
configureVueProject({ scriptLangs: ['js', 'jsx', 'ts', 'tsx'] })
// More info at https://github.com/vuejs/eslint-config-typescript/#advanced-setup

export default defineConfigWithVueTs(
  {
    name: 'app/files-to-lint',
    files: ['**/*.{ts,mts,tsx,vue}'],
  },

  pluginVue.configs['flat/essential'],
  vueTsConfigs.recommended,
  {
    languageOptions: {
      parserOptions: {
        sourceType: 'module',
        ecmaVersion: 'latest',
        extraFileExtensions: ['.vue'],
        // 确保启用 TypeScript 功能
        project: './tsconfig.app.json'
      },
    }
  },

  {
    ...pluginVitest.configs.recommended,
    files: ['src/**/__tests__/*'],
  },
  {
    files: ['**/*.vue'],
    rules: {
      'vue/block-lang': 'off'
    }
  },

  skipFormatting,
)
